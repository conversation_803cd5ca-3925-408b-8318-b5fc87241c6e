#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证持仓记录格式脚本
检查所有持仓记录文件是否都已转换为新格式
"""

import json
import os
import glob

def check_position_format(position_data, code):
    """检查单个持仓记录的格式"""
    issues = []
    
    # 检查是否是旧格式（有buy_price和quantity，但没有buy_queue）
    if ('buy_price' in position_data and 
        'quantity' in position_data and 
        'buy_queue' not in position_data):
        issues.append(f"  ❌ {code}: 仍然是旧格式（直接包含buy_price和quantity）")
        return issues
    
    # 检查是否是新格式
    if 'buy_queue' in position_data:
        # 验证新格式的完整性
        if not isinstance(position_data['buy_queue'], list):
            issues.append(f"  ❌ {code}: buy_queue不是列表格式")
        elif len(position_data['buy_queue']) == 0:
            issues.append(f"  ⚠️  {code}: buy_queue为空")
        else:
            # 检查buy_queue中的每个记录
            for i, buy_record in enumerate(position_data['buy_queue']):
                required_fields = ['buy_price', 'buy_time', 'quantity', 'fee', 'actual_amount', 'order_id']
                for field in required_fields:
                    if field not in buy_record:
                        issues.append(f"  ❌ {code}: buy_queue[{i}]缺少必需字段'{field}'")
        
        # 检查总计字段
        required_totals = ['total_quantity', 'total_cost', 'total_fee']
        for field in required_totals:
            if field not in position_data:
                issues.append(f"  ❌ {code}: 缺少总计字段'{field}'")
        
        if not issues:
            issues.append(f"  ✅ {code}: 新格式正确")
    else:
        issues.append(f"  ❓ {code}: 未知格式（既没有buy_queue也没有buy_price+quantity）")
    
    return issues

def check_position_file(file_path):
    """检查单个持仓记录文件"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"\n📁 检查文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                print("  ⚠️  文件为空")
                return True
            
            position_records = json.loads(content)
        
        if not position_records:
            print("  ⚠️  没有持仓记录")
            return True
        
        all_issues = []
        old_format_count = 0
        new_format_count = 0
        
        for code, position_data in position_records.items():
            issues = check_position_format(position_data, code)
            all_issues.extend(issues)
            
            # 统计格式类型
            if ('buy_price' in position_data and 
                'quantity' in position_data and 
                'buy_queue' not in position_data):
                old_format_count += 1
            elif 'buy_queue' in position_data:
                new_format_count += 1
        
        # 输出检查结果
        for issue in all_issues:
            print(issue)
        
        print(f"\n📊 统计结果:")
        print(f"  - 新格式记录: {new_format_count}")
        print(f"  - 旧格式记录: {old_format_count}")
        print(f"  - 总记录数: {len(position_records)}")
        
        if old_format_count == 0:
            print("  🎉 所有记录都已转换为新格式！")
            return True
        else:
            print(f"  ⚠️  还有 {old_format_count} 条记录需要转换")
            return False
        
    except json.JSONDecodeError as e:
        print(f"  ❌ JSON格式错误: {str(e)}")
        return False
    except Exception as e:
        print(f"  ❌ 检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 开始验证持仓记录格式...")
    
    # 需要检查的文件列表
    files_to_check = [
        "自选股30m系统持仓记录.json",
        "可转债放量方法持仓记录.json"
    ]
    
    # 查找其他可能的持仓记录文件
    additional_files = glob.glob("*持仓记录*.json")
    for file_path in additional_files:
        if file_path not in files_to_check and not file_path.endswith('.backup'):
            files_to_check.append(file_path)
    
    print(f"📋 发现 {len(files_to_check)} 个持仓记录文件")
    
    success_count = 0
    for file_path in files_to_check:
        if check_position_file(file_path):
            success_count += 1
    
    print(f"\n🏁 验证完成！")
    print(f"✅ 格式正确的文件: {success_count}/{len(files_to_check)}")
    
    if success_count == len(files_to_check):
        print("🎉 所有持仓记录文件都已成功转换为新格式！")
        return True
    else:
        print("⚠️  部分文件仍需要格式转换")
        return False

if __name__ == "__main__":
    main()
