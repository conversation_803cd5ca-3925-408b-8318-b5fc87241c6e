import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from stock_utils import StockCodeParser, QMTConnector, DataDownloader
from stock_utils import show_time_series, check_cross_signals
import threading
import time
from datetime import datetime, timedelta
from xtquant import xtdata
import pandas as pd
import json
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
import socket
from xtquant.xtconstant import STOCK_BUY, FIX_PRICE, STOCK_SELL
import random
from signal_analyzer import SignalAnalyzer

class TimeSeriesViewer:
    def __init__(self):
        # 设置日志文件 - 移到最前面
        self.log_file = "trading_log.txt"
        self.setup_logging()
        
        # 初始化交易指令字典
        self.trade_instructions = {}
        
        # 新增：初始化等待盈利卖出字典
        self.waiting_for_profit_sell = {}
        
        # 新增：初始化等待EXP3突破字典
        self.waiting_for_exp3_break = {}

        # 新增：初始化止盈监控字典
        self.profit_taking_monitor = {}  # 用于记录已达到止盈条件的股票的上一周期价格

        # 新增：初始化技术指标卖出等待重新买回字典
        self.tech_sell_waiting_rebuy = {}  # 用于记录技术指标卖出等待重新买回的股票
        
        # 新增：初始化持仓统计变量
        self.max_positions_today = 0
        self.max_profit_today = 0.0
        self.max_loss_today = 0.0
        
        # 新增：初始化当日最大浮盈和最大浮亏
        self.daily_max_float_profit = 0.0  # 当日最大浮盈（总持仓）
        self.daily_max_float_loss = 0.0    # 当日最大浮亏（总持仓）
        self.daily_total_profit = 0.0      # 当日总盈亏（包括已平仓）
        
        # 新增：初始化单只股票最大盈亏记录
        self.single_stock_max_profit = 0.0  # 当日单只股票最大浮盈
        self.single_stock_max_loss = 0.0    # 当日单只股票最大浮亏
        
        # 新增：记录当前日期和统计保存状态
        self.current_date = datetime.now().strftime('%Y%m%d')
        self.daily_stats_saved = False

        # 初始化收盘后价格更新标记
        self.closing_price_updated = False
        self.refresh_stopped_logged = False
        
        # 初始化其他属性
        self.root = tk.Tk()
        self.root.title("可转债做T交易系统")
        self.root.geometry("1200x800")
        
        # 定义文件路径 - 持仓记录使用固定文件名，交易历史使用日期
        self.today = datetime.now().strftime('%Y%m%d')
        self.position_file = "可转债放量方法持仓记录.json"  # 固定文件名，支持跨交易日延续
        self.trade_history_file = f"交易历史_{self.today}.json"
        self.waiting_break_file = f"等待突破_{self.today}.json"  # 新增：等待突破记录文件
        
        # 加载交易数据
        self.trading_data = self.load_trading_data()
        self.position_records = self.trading_data.get('positions', {})
        self.trade_records = self.trading_data.get('trades', [])
        self.cross_records = self.trading_data.get('cross_records', {})
        self.waiting_for_exp3_break = self.trading_data.get('waiting_break', {})  # 新增：等待突破字典
        self.tech_sell_waiting_rebuy = self.trading_data.get('tech_sell_waiting_rebuy', {})  # 新增：技术指标卖出等待重新买回字典
        
        # 设置交易参数
        self.trading_enabled = False
        self.monitoring_enabled = False
        self.strategy_params = {
            'max_wait_periods': 5,  # 最大等待周期数
            'min_profit': 100,      # 最小盈利金额
            'max_loss': -100,       # 最大亏损金额
            'price_adjust': 0.20    # 价格调整幅度
        }
        
        # 初始化QMT连接和数据下载器
        self.qmt = QMTConnector()
        if not self.qmt.ensure_connection():
            messagebox.showerror("错误", "无法连接到QMT")
            raise Exception("无法连接到QMT")
        
        self.downloader = DataDownloader()
        self.parser = StockCodeParser()
        
        # 交易相关变量
        self.trading_enabled = False  # 是否启用自动交易
        self.is_trading = False  # 是否正在交易
        
        # 添加连接状态变量
        self.is_connected = False
        
        # 创建界面元素
        self.create_widgets()

        # 设置股票池文件路径
        self.stock_pool_file = "stock_pool.json"

        # 自动加载股票池
        self.load_stock_pool_from_file()
        
        # 初始化监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.order_monitor_thread = None  # 添加委托监控线程
        
        # 更新持仓列表和交易汇总（启动时获取一次价格）
        self.add_record("🚀 程序启动，获取持仓价格...")
        self.update_position_list()
        self.update_trade_summary()
        
        # 添加首次启动标记
        self.first_run = True
        
        # 启动连接状态检查
        self.check_connection()
        
        # 添加策略参数
        self.strategy_params = {
            'm1': 12,  # 快速EMA周期
            'm2': 50,  # 慢速EMA周期
            'n': 14,   # ATR周期
            'm': 1.0,  # ATR乘数
            'max_wait_periods': 3  # 等待第二个信号的最大周期数
        }
        
        # 添加策略状态变量
        self.first_cross = None  # 记录第一次交叉信号
        self.wait_periods = 0    # 等待第二个信号的周期数
        
        # 启动持仓价格定时刷新
        self.start_price_refresh()
        
        # 在程序启动时处理成交回报，确保持仓价格准确
        if self.trading_enabled and self.position_records:
            self.add_record("程序启动时处理成交回报，更新持仓价格...")
            self.process_all_trade_reports()
    
        # 加载状态信息
        self.load_state()

        # 初始化自动刷新相关变量
        self.auto_refresh_times = ['09:27', '10:37', '11:37', '14:07']
        self.last_auto_refresh_time = None
        self.volume_bond_file = "放量可转债.txt"

        # 初始化获利股票黑名单
        self.profit_blacklist_file = "获利股票黑名单.json"
        self.profit_blacklist = self.load_profit_blacklist()

        # 添加9:28检查时间
        self.blacklist_check_times = ['09:28']
        self.last_blacklist_check_time = None

    def setup_logging(self):
        """设置日志文件"""
        try:
            # 创建log文件夹（如果不存在）
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'log')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 设置日志文件路径
            current_date = datetime.now().strftime('%Y%m%d')
            self.log_file = os.path.join(log_dir, f'trading_log_{current_date}.txt')

            # 检查日志文件是否已存在
            if not os.path.exists(self.log_file):
                # 如果文件不存在，创建新文件并写入头部信息
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"=== 交易日志 {current_date} ===\n")
                    f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                print(f"日志文件已创建: {self.log_file}")
            else:
                # 如果文件已存在，追加程序重启信息
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n=== 程序重启 ===\n")
                    f.write(f"重启时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                print(f"日志文件已存在，将追加记录: {self.log_file}")

        except Exception as e:
            print(f"设置日志文件失败: {str(e)}")
            self.log_file = None

    def write_log(self, text):
        """写入日志文件"""
        try:
            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_text = f"[{timestamp}] {text}"
            
            # 输出到日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_text + '\n')
                
        except Exception as e:
            print(f"写入日志失败: {str(e)}")
    
    def load_trading_data(self):
        """从文件加载交易数据"""
        # 检查是否有当日交易记录
        today = datetime.now().strftime('%Y%m%d')
        # 持仓记录使用固定文件名，支持跨交易日延续
        # self.position_file = "可转债放量方法持仓记录.json"  # 已在__init__中定义
        self.trade_history_file = f"交易历史_{today}.json"
        self.waiting_break_file = f"等待突破_{today}.json"  # 新增：等待突破记录文件
        self.tech_sell_file = f"技术指标卖出等待重新买回_{today}.json"  # 新增：技术指标卖出等待重新买回记录文件
        
        # 初始化交易记录
        self.trade_records = []
        self.cross_records = {}
        self.waiting_for_exp3_break = {}  # 初始化等待突破字典
        self.tech_sell_waiting_rebuy = {}  # 初始化技术指标卖出等待重新买回字典
        
        # 初始化当日总盈亏
        self.daily_total_profit = 0.0
        
        # 加载持仓记录
        if os.path.exists(self.position_file):
            try:
                with open(self.position_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:  # 如果文件为空
                        self.position_records = {}
                        print("持仓记录文件为空，初始化空记录")
                    else:
                        self.position_records = json.loads(content)
                        print(f"已加载 {len(self.position_records)} 条当日持仓记录")
            except json.JSONDecodeError as e:
                print(f"持仓记录文件格式错误: {str(e)}")
                self.position_records = {}
            except Exception as e:
                print(f"加载持仓记录失败: {str(e)}")
                self.position_records = {}
        else:
            print(f"未找到当日持仓记录，初始化空记录")
            self.position_records = {}
        
        # 加载交易历史
        if os.path.exists(self.trade_history_file):
            try:
                with open(self.trade_history_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        self.trade_records = json.loads(content)
                        print(f"已加载 {len(self.trade_records)} 条当日交易记录")
                        # 计算当日总盈亏
                        self.daily_total_profit = sum(record['profit'] for record in self.trade_records)
                        self.daily_total_profit = round(self.daily_total_profit, 2)
                        print(f"当日总盈亏: {self.daily_total_profit:.2f}")
            except json.JSONDecodeError as e:
                print(f"交易历史文件格式错误: {str(e)}")
                self.trade_records = []
            except Exception as e:
                print(f"加载交易历史失败: {str(e)}")
                self.trade_records = []
        
        # 新增：加载等待突破记录
        if os.path.exists(self.waiting_break_file):
            try:
                with open(self.waiting_break_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        self.waiting_for_exp3_break = json.loads(content)
                        print(f"已加载 {len(self.waiting_for_exp3_break)} 条等待突破记录")
            except json.JSONDecodeError as e:
                print(f"等待突破记录文件格式错误: {str(e)}")
                self.waiting_for_exp3_break = {}
            except Exception as e:
                print(f"加载等待突破记录失败: {str(e)}")
                self.waiting_for_exp3_break = {}

        # 新增：加载技术指标卖出等待重新买回记录
        if os.path.exists(self.tech_sell_file):
            try:
                with open(self.tech_sell_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        self.tech_sell_waiting_rebuy = json.loads(content)
                        print(f"已加载 {len(self.tech_sell_waiting_rebuy)} 条技术指标卖出等待重新买回记录")
            except json.JSONDecodeError as e:
                print(f"技术指标卖出等待重新买回记录文件格式错误: {str(e)}")
                self.tech_sell_waiting_rebuy = {}
            except Exception as e:
                print(f"加载技术指标卖出等待重新买回记录失败: {str(e)}")
                self.tech_sell_waiting_rebuy = {}
        
        # 保存空记录（如果文件不存在）
        self.save_trading_data()
        
        return {
            'positions': self.position_records,
            'trades': self.trade_records,
            'cross_records': self.cross_records,
            'waiting_break': self.waiting_for_exp3_break,  # 新增：返回等待突破记录
            'tech_sell_waiting_rebuy': self.tech_sell_waiting_rebuy  # 新增：返回技术指标卖出等待重新买回记录
        }
    
    def save_trading_data(self):
        """保存交易数据到文件"""
        today = datetime.now().strftime('%Y%m%d')

        # 保存持仓记录 - 使用固定文件名，支持跨交易日延续
        with open(self.position_file, 'w', encoding='utf-8') as f:
            json.dump(self.position_records, f, ensure_ascii=False, indent=2)

        # 保存交易历史 - 使用日期文件名
        with open(f"交易历史_{today}.json", 'w', encoding='utf-8') as f:
            json.dump(self.trade_records, f, ensure_ascii=False, indent=2)

        # 保存技术指标卖出等待重新买回记录 - 使用日期文件名
        with open(f"技术指标卖出等待重新买回_{today}.json", 'w', encoding='utf-8') as f:
            json.dump(self.tech_sell_waiting_rebuy, f, ensure_ascii=False, indent=2)

        # 保存状态信息
        self.save_state()
    
    def create_widgets(self):
        """创建界面元素"""
        # 创建标签框架
        control_frame = ttk.LabelFrame(self.root, text="控制面板")
        control_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建按钮
        self.monitor_button = ttk.Button(control_frame, text="开始监控", command=self.toggle_monitoring)
        self.monitor_button.grid(row=0, column=0, padx=5, pady=5)
        
        self.load_button = ttk.Button(control_frame, text="加载代码", command=self.load_stock_codes)
        self.load_button.grid(row=0, column=1, padx=5, pady=5)
        
        # 导入导出按钮
        self.import_button = ttk.Button(control_frame, text="导入数据", command=self.import_trading_data)
        self.import_button.grid(row=0, column=2, padx=5, pady=5)
        
        self.export_button = ttk.Button(control_frame, text="导出数据", command=self.export_trading_data)
        self.export_button.grid(row=0, column=3, padx=5, pady=5)
        
        # 添加测试按钮
        self.test_button = ttk.Button(control_frame, text="测试功能", command=self.test_order_functions)
        self.test_button.grid(row=0, column=4, padx=5, pady=5)

        # 添加统计报表按钮
        self.stats_button = ttk.Button(control_frame, text="统计报表", command=self.show_trading_statistics)
        self.stats_button.grid(row=0, column=5, padx=5, pady=5)

        # 添加自动刷新按钮
        self.auto_refresh_button = ttk.Button(control_frame, text="刷新股票池", command=self.manual_refresh_stock_pool)
        self.auto_refresh_button.grid(row=0, column=6, padx=5, pady=5)

        # 添加手动刷新持仓价格按钮
        self.refresh_position_button = ttk.Button(control_frame, text="刷新持仓", command=self.manual_refresh_positions)
        self.refresh_position_button.grid(row=0, column=7, padx=5, pady=5)

        # 创建交易开关
        self.trading_var = tk.BooleanVar(value=True)
        self.trading_check = ttk.Checkbutton(
            control_frame, 
            text="启用交易", 
            variable=self.trading_var,
            command=self.toggle_trading
        )
        self.trading_check.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        # 添加连接状态标签
        self.connection_status_label = ttk.Label(control_frame, text="连接状态: 未连接", foreground="red")
        self.connection_status_label.grid(row=0, column=8, padx=5, pady=5)

        # 添加实时提示标签
        self.realtime_hint_label = ttk.Label(control_frame, text="实时提示: 等待监控开始...", wraplength=400)
        self.realtime_hint_label.grid(row=2, column=0, columnspan=9, padx=5, pady=5, sticky="w")
        
        # 新增：添加持仓统计标签
        self.position_stats_label = ttk.Label(control_frame, text="当前持仓: 0 | 今日最大持仓: 0")
        self.position_stats_label.grid(row=1, column=2, columnspan=4, padx=5, pady=5, sticky="w")

        # 添加黑名单状态标签
        blacklist_count = len(getattr(self, 'profit_blacklist', set()))
        self.blacklist_status_label = ttk.Label(control_frame, text=f"获利黑名单: {blacklist_count} 只股票")
        self.blacklist_status_label.grid(row=1, column=6, columnspan=2, padx=5, pady=5, sticky="w")
        
        # 创建左右分栏
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 右侧栏 - 交易记录（先创建右侧栏，确保record_listbox最先可用）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill="both", expand=True)
        
        # 创建记录列表
        record_frame = ttk.LabelFrame(right_frame, text="交易记录")
        record_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 添加滚动
        record_scrollbar = tk.Scrollbar(record_frame)
        record_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.record_listbox = tk.Listbox(record_frame, yscrollcommand=record_scrollbar.set)
        self.record_listbox.pack(fill="both", expand=True)
        record_scrollbar.config(command=self.record_listbox.yview)
        
        # 左侧栏 - 股票代码和持仓
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 创建可转债代码框架
        codes_frame = ttk.Frame(left_frame)
        codes_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建可转债代码列表框
        bond_frame = ttk.LabelFrame(codes_frame, text="可转债代码")
        bond_frame.pack(fill="both", expand=True)

        # 添加滚动条
        bond_scrollbar = tk.Scrollbar(bond_frame)
        bond_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.bond_listbox = tk.Listbox(bond_frame, yscrollcommand=bond_scrollbar.set)
        self.bond_listbox.pack(fill="both", expand=True)
        bond_scrollbar.config(command=self.bond_listbox.yview)

        # 绑定双击事件
        self.bond_listbox.bind('<Double-1>', self.show_selected_stock)

        # 绑定右键菜单事件
        self.bond_listbox.bind('<Button-3>', self.show_stock_menu)
        
        # 创建持仓列表
        position_frame = ttk.LabelFrame(left_frame, text="当前持仓")
        position_frame.pack(fill="both", expand=False, padx=5, pady=5, ipady=30)
        
        # 添加滚动条
        position_scrollbar = tk.Scrollbar(position_frame)
        position_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.position_listbox = tk.Listbox(position_frame, yscrollcommand=position_scrollbar.set)
        self.position_listbox.pack(fill="both", expand=True)
        position_scrollbar.config(command=self.position_listbox.yview)
        
        # 绑定点击事件到持仓列表框
        self.position_listbox.bind('<Double-1>', self.show_position_stock)
        self.position_listbox.bind('<Button-3>', self.show_position_menu)
        
        # 创建持仓右键菜单
        self.position_menu = tk.Menu(self.root, tearoff=0)
        self.position_menu.add_command(label="查看分时图", command=self.view_selected_position)
        self.position_menu.add_command(label="清除持仓", command=self.clear_selected_position)

        # 创建股票列表右键菜单
        self.stock_menu = tk.Menu(self.root, tearoff=0)
        self.stock_menu.add_command(label="查看分时图", command=self.view_selected_stock)
        self.stock_menu.add_command(label="从股票池中移除", command=self.remove_selected_stock)
        
        # 创建交易汇总
        summary_frame = ttk.LabelFrame(left_frame, text="交易汇总")
        summary_frame.pack(fill="x", padx=5, pady=5)
        
        self.trade_summary_label = ttk.Label(summary_frame, text="", wraplength=400)
        self.trade_summary_label.pack(anchor="w", padx=5, pady=2)
    
    def load_stock_codes(self):
        """加载股票代码"""
        try:
            # 通过对话框选择可转债文件
            bond_file_path = filedialog.askopenfilename(
                title="选择可转债代码文件",
                filetypes=[("通达信板块文件", "*.blk"), ("所有文件", "*.*")],
                initialdir=os.getcwd()  # 使用当前目录
            )

            if not bond_file_path:  # 用户取消了选择
                return

            # 读取新的可转债代码
            new_bond_codes = self.parser.read_stock_codes(bond_file_path)

            # 检查是否有新代码
            if not new_bond_codes:
                messagebox.showwarning("警告", "未找到有效的可转债代码")
                return

            # 询问是否清除原有股票池（默认为追加）
            clear_existing = False
            if hasattr(self, 'bond_codes') and self.bond_codes:
                # 如果已有股票代码，询问用户
                result = messagebox.askyesnocancel(
                    "股票池操作",
                    f"当前股票池已有 {len(getattr(self, 'bond_codes', []))} 只可转债。\n\n"
                    f"新文件包含 {len(new_bond_codes)} 只可转债。\n\n"
                    "是否清除原有股票池？\n\n"
                    "【是】：清除原有，只保留新加载的股票\n"
                    "【否】：追加到原有股票池（默认推荐）\n"
                    "【取消】：取消本次加载操作"
                )

                if result is None:  # 用户点击取消
                    return
                elif result:  # 用户点击是，清除原有
                    clear_existing = True
                # else: result为False，用户点击否，追加模式

            if clear_existing or not hasattr(self, 'bond_codes'):
                # 清除模式或首次加载
                self.bond_codes = new_bond_codes
                operation_text = "替换"
            else:
                # 追加模式
                # 去重合并可转债代码
                existing_bond_set = set(getattr(self, 'bond_codes', []))
                for code in new_bond_codes:
                    if code not in existing_bond_set:
                        self.bond_codes.append(code)
                        existing_bond_set.add(code)

                operation_text = "追加"

            # 初始化ETF代码为空列表（保持兼容性）
            self.etf_codes = []

            # 合并所有代码用于监控（现在只有可转债）
            self.stock_codes = self.bond_codes

            # 清空并重新填充可转债列表框
            self.bond_listbox.delete(0, tk.END)
            for code in self.bond_codes:
                self.bond_listbox.insert(tk.END, code)

            # 更新状态
            total_codes = len(self.bond_codes)
            self.root.title(f"可转债做T交易 - {total_codes}只可转债")

            # 记录加载成功信息
            self.add_record(f"成功{operation_text} {len(new_bond_codes)} 只可转债代码")
            self.add_record(f"当前股票池总计: {total_codes} 只可转债")
            print(f"成功{operation_text}可转债代码，当前总计 {total_codes} 只")

            # 保存股票池到文件
            self.save_stock_pool_to_file()

        except Exception as e:
            messagebox.showerror("错误", f"加载股票代码失败: {str(e)}")
            print(f"加载股票代码失败: {str(e)}")
    
    def filter_stocks(self, *args):
        """根据搜索框内容过滤股票列表"""
        search_text = self.search_var.get().upper()
        self.stock_listbox.delete(0, tk.END)
        
        for code in self.stock_codes:
            if search_text in code:
                # 不再为11开头的可转债添加标记
                self.stock_listbox.insert(tk.END, code)
    
    def show_selected_stock(self, event):
        """显示选中股票的分时图"""
        # 确定是哪个列表框触发了事件
        widget = event.widget
        selection = widget.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个股票")
            return
            
        code = widget.get(selection[0])
        # 移除可能的标记
        code = code.split(" ")[0]
        
        # 首先在后台线程中检查股票是否可交易
        def check_and_show():
            try:
                # 检查股票代码是否有效
                is_valid = self.is_valid_for_trading(code)
                if not is_valid:
                    self.root.after(0, lambda: messagebox.showinfo("提示", f"{code} 当前不可交易或数据不可用"))
                    return
                
                # 获取数据
                today = datetime.now().strftime('%Y%m%d')
                self.downloader.my_download([code], '1m', today, today)
                time.sleep(1)  # 等待数据就绪
                
                # 在主线程中显示图表
                self.root.after(0, lambda: self.display_chart(code))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"准备显示分时图失败: {error_msg}"))
        
        threading.Thread(target=check_and_show).start()
    
    def display_chart(self, code):
        """显示股票分时图"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取分时数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='1m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            if not minute_data or code not in minute_data['close'].index or len(minute_data['close'].loc[code]) == 0:
                messagebox.showinfo("提示", f"无法获取 {code} 的分时数据")
                return
            
            # 创建新窗口
            chart_window = tk.Toplevel(self.root)
            chart_window.title(f"{code} 分时图")
            chart_window.geometry("800x600")
            
            # 创建图表
            fig = Figure(figsize=(10, 8), dpi=100)
            
            # 创建价格子图
            ax1 = fig.add_subplot(211)  # 2行1列的第1个
            
            # 获取时间和价格数据
            times = minute_data['time'].loc[code]
            prices = minute_data['close'].loc[code]
            
            # 计算均价线 - 使用累计成交额除以累计成交量
            amounts = minute_data['amount'].loc[code]
            volumes = minute_data['volume'].loc[code]
            
            # 计算累计成交额和累计成交量
            cumulative_amounts = np.cumsum(amounts)
            cumulative_volumes = np.cumsum(volumes)
            
            # 避免除以零
            valid_volumes = cumulative_volumes > 0
            avg_prices = np.zeros_like(prices)
            avg_prices[valid_volumes] = cumulative_amounts[valid_volumes] / cumulative_volumes[valid_volumes]
            
            # 根据股票类型调整均价除数
            if code.startswith(('15', '5')):  # ETF
                avg_prices = avg_prices / 100
            else:  # 可转债
                avg_prices = avg_prices / 10
            
            # 计算20分钟移动平均线 - 修改为使用min_periods参数
            ma20 = pd.Series(prices).rolling(window=20, min_periods=1).mean().values
            
            # 创建完整的交易时间轴
            full_time_axis = []
            
            # 上午交易时段 9:30-11:30
            morning_start = datetime.strptime(f"{today} 09:30:00", "%Y%m%d %H:%M:%S")
            morning_end = datetime.strptime(f"{today} 11:30:00", "%Y%m%d %H:%M:%S")
            current = morning_start
            while current <= morning_end:
                full_time_axis.append(current.strftime('%H:%M'))
                current += timedelta(minutes=1)
            
            # 下午交易时段 13:00-15:00
            afternoon_start = datetime.strptime(f"{today} 13:00:00", "%Y%m%d %H:%M:%S")
            afternoon_end = datetime.strptime(f"{today} 15:00:00", "%Y%m%d %H:%M:%S")
            current = afternoon_start
            while current <= afternoon_end:
                full_time_axis.append(current.strftime('%H:%M'))
                current += timedelta(minutes=1)
            
            # 格式化实际数据的时间标签
            actual_time_labels = []
            for t in times:
                if isinstance(t, datetime):
                    actual_time_labels.append(t.strftime('%H:%M'))
                else:
                    # 将毫秒时间戳转换为datetime对象
                    dt = datetime.fromtimestamp(t/1000) if t > 1000000000 else datetime.now()
                    actual_time_labels.append(dt.strftime('%H:%M'))
            
            # 创建映射，将实际数据点映射到完整时间轴上
            data_indices = []
            for label in actual_time_labels:
                if label in full_time_axis:
                    data_indices.append(full_time_axis.index(label))
                else:
                    # 如果找不到精确匹配，尝试找最接近的时间点
                    closest = min(full_time_axis, key=lambda x: abs(datetime.strptime(x, '%H:%M') - datetime.strptime(label, '%H:%M')))
                    data_indices.append(full_time_axis.index(closest))
            
            # 创建扩展的数据数组，用于在完整时间轴上绘图
            extended_prices = np.full(len(full_time_axis), np.nan)
            extended_avg_prices = np.full(len(full_time_axis), np.nan)
            extended_ma20 = np.full(len(full_time_axis), np.nan)
            extended_volumes = np.full(len(full_time_axis), 0)
            
            # 填充实际数据
            for i, idx in enumerate(data_indices):
                if idx < len(extended_prices):
                    # 修复：使用正确的索引方式访问numpy数组
                    if isinstance(prices, pd.Series):
                        extended_prices[idx] = prices.iloc[i]
                    else:
                        extended_prices[idx] = prices[i]
                        
                    if isinstance(avg_prices, pd.Series):
                        extended_avg_prices[idx] = avg_prices.iloc[i]
                    else:
                        extended_avg_prices[idx] = avg_prices[i]
                        
                    extended_ma20[idx] = ma20[i]  # ma20是numpy数组，保持不变
                    
                    if isinstance(volumes, pd.Series):
                        extended_volumes[idx] = volumes.iloc[i]
                    else:
                        extended_volumes[idx] = volumes[i]
            
            # 绘制价格线和均价线 - 使用完整时间轴
            ax1.plot(full_time_axis, extended_prices, 'b-', label='价格')
            ax1.plot(full_time_axis, extended_avg_prices, 'r-', label='均价(÷10)')
            ax1.plot(full_time_axis, extended_ma20, 'g-', label='MA20')  # 添加20分钟移动平均线
            
            # 如果有持仓，标记买入价格
            if code in self.position_records:
                buy_price = self.position_records[code]['buy_price']
                ax1.axhline(y=buy_price, color='m', linestyle='--', label=f'买入价: {buy_price:.2f}')
            
            # 设置标题和标签
            ax1.set_title(f"{code} 分时图")
            ax1.set_ylabel('价格')
            ax1.grid(True)
            ax1.legend()
            
            # 设置x轴标签 - 显示关键时间点
            key_times = ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']
            key_indices = [full_time_axis.index(t) for t in key_times if t in full_time_axis]
            
            ax1.set_xticks(key_indices)
            ax1.set_xticklabels([full_time_axis[i] for i in key_indices], rotation=45)
            
            # 创建成交量子图
            ax2 = fig.add_subplot(212, sharex=ax1)  # 2行1列的第2个，共享x轴
            
            # 绘制成交量柱状图 - 使用完整时间轴
            if np.sum(extended_volumes) > 0:
                bars = ax2.bar(range(len(full_time_axis)), extended_volumes, width=0.8, color='g', alpha=0.5)
                
                # 设置y轴范围
                max_volume = np.max(extended_volumes)
                if max_volume > 0:
                    ax2.set_ylim(0, max_volume * 1.2)
                    
                    # 为较大的成交量添加数值标签
                    threshold = max_volume * 0.5
                    for i, v in enumerate(extended_volumes):
                        if v >= threshold:
                            ax2.text(i, v, str(int(v)), ha='center', va='bottom', fontsize=8)
            else:
                ax2.text(0.5, 0.5, '无成交量数据', 
                        horizontalalignment='center',
                        verticalalignment='center',
                        transform=ax2.transAxes)
            
            # 设置标签
            ax2.set_ylabel('成交量')
            ax2.set_xlabel('时间')
            ax2.grid(True)
            
            # 确保x轴标签与价格图对齐
            ax2.set_xticks(ax1.get_xticks())
            ax2.set_xticklabels(ax1.get_xticklabels())
            
            # 调整布局
            fig.tight_layout()
            
            # 将图表添加到窗口
            canvas = FigureCanvasTkAgg(fig, master=chart_window)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            toolbar = NavigationToolbar2Tk(canvas, chart_window)
            toolbar.update()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
        except Exception as e:
            messagebox.showerror("错误", f"显示分时图失败: {str(e)}")
            print(f"显示分时图失败: {str(e)}")
    
    def display_5min_chart(self, code):
        """使用5分钟数据显示分时图"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 尝试获取5分钟数据
            self.downloader.my_download([code], '5m', today, today)
            time.sleep(1)  # 等待数据就绪
            
            # 获取5分钟K线数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='5m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            # 检查是否获取到数据
            if minute_data is None or code not in minute_data['time'].index or len(minute_data['time'].loc[code]) == 0:
                print(f"无法获取{code}的5分钟数据")
                messagebox.showinfo("提示", f"无法获取{code}的分时数据，请使用K线图查看")
                return
                
            # 创建DataFrame
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms') + pd.Timedelta(hours=8),
                'open': minute_data['open'].loc[code],
                'close': minute_data['close'].loc[code],
                'high': minute_data['high'].loc[code],
                'low': minute_data['low'].loc[code],
                'volume': minute_data['volume'].loc[code],
                'amount': minute_data['amount'].loc[code]
            })
            
            # 绘制分时图
            from stock_utils import plot_time_series
            plot_time_series(df, f"{code} 分时图 (5分钟)")
            
        except Exception as e:
            print(f"显示5分钟分时图时出错: {str(e)}")
            messagebox.showerror("错误", f"显示分时图失败: {str(e)}")
    
    def is_valid_for_trading(self, code, timeout=3):
        """检查股票是否可交易且数据可用，使用线程超时机制"""
        result = [False]
        error_msg = [None]
        
        def check_trading():
            try:
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 尝试获取分时数据，使用正确的参数格式
                self.downloader.my_download([code], '1m', today, today)
                
                # 等待数据就绪
                time.sleep(1)
                
                # 获取1分钟K线数据
                minute_data = xtdata.get_market_data(
                    field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                    stock_list=[code],
                    period='1m',
                    start_time=today,
                    end_time=today,
                    count=-1
                )
                
                # 检查返回的数据是否有效
                if minute_data and code in minute_data['time'].index and len(minute_data['time'].loc[code]) > 0:
                    result[0] = True
                else:
                    result[0] = False
            except Exception as e:
                error_msg[0] = str(e)
                result[0] = False
                # 只打印错误，不使用messagebox
                print(f"检查股票{code}交易状态时出错: {str(e)}")
        
        # 创建并启动检查线程
        check_thread = threading.Thread(target=check_trading)
        check_thread.daemon = True
        check_thread.start()
        
        # 等待线程完成，带超时
        check_thread.join(timeout)
        
        # 如果线程仍在运行，说明超时了
        if check_thread.is_alive():
            print(f"检查股票{code}交易状态超时")
            return False
        
        return result[0]
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_button.config(text="停止监控")
            self.update_realtime_hint("监控已启动...")
            
            # 创建并启动监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_stocks)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            # 创建并启动委托监控线程
            self.order_monitor_thread = threading.Thread(target=self.monitor_orders)
            self.order_monitor_thread.daemon = True
            self.order_monitor_thread.start()
            
            print("开始监控")
        else:
            # 停止监控
            self.monitoring = False
            self.monitor_button.config(text="开始监控")
            self.update_realtime_hint("监控已停止")
            
            # 等待线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(1.0)
            if self.order_monitor_thread and self.order_monitor_thread.is_alive():
                self.order_monitor_thread.join(1.0)
            
            print("停止监控")
    
    def toggle_trading(self):
        """切换自动交易状态"""
        self.trading_enabled = self.trading_var.get()
        status = "启用" if self.trading_enabled else "禁用"
        self.add_record(f"自动交易已{status}")
    
    def export_trade_records(self):
        """导出交易记录到CSV文件"""
        if not self.trade_records:
            messagebox.showinfo("提示", "没有交易记录可导出")
            return
            
        try:
            # 获取当前日期时间作为文件名
            now = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"交易记录_{now}.csv"
            
            # 创建DataFrame并导出
            df = pd.DataFrame(self.trade_records)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            messagebox.showinfo("成功", f"交易记录已导出至 {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出交易记录失败: {str(e)}")
    
    def monitor_stocks(self):
        """监控股票"""
        try:
            self.monitoring = True
            self.add_record("开始监控股票...")
            
            # 添加清理标记
            self.cleanup_done = False
            # 添加上一次检查的分钟标记
            self.last_check_minute = -1
            # 添加收市提示标记
            self.market_closed_hint_shown = False
            # 添加9:30第一次检查标记
            self.first_930_check_done = False

            # 启动时立即进行一次信号检查
            print("执行初始检查...")
            self.add_record("进行初始买卖点判定...")

            # 检查当前时间，如果是9:30则跳过买入
            current_time = datetime.now().time()
            if current_time.strftime('%H:%M') == '09:30':
                self.add_record("⏰ 9:30第一次检查，跳过买入操作，只进行卖出检查")
                self.check_trading_signals(is_after_1430=False, skip_buy=True)
                self.first_930_check_done = True
            else:
                self.check_trading_signals(is_after_1430=False)
            
            while self.monitoring:
                try:
                    # 获取当前时间
                    now = datetime.now()
                    current_time = now.time()
                    current_time_str = current_time.strftime('%H:%M')

                    # 检查是否需要自动刷新股票池
                    if (current_time_str in self.auto_refresh_times and
                        self.last_auto_refresh_time != current_time_str):
                        self.add_record(f"⏰ 到达自动刷新时间 {current_time_str}，开始刷新股票池...")
                        self.auto_refresh_stock_pool()
                        self.last_auto_refresh_time = current_time_str

                    # 检查是否需要清理获利黑名单（9:28）
                    if (current_time_str in self.blacklist_check_times and
                        self.last_blacklist_check_time != current_time_str):
                        self.add_record(f"⏰ 到达黑名单检查时间 {current_time_str}，开始清理获利黑名单...")
                        self.clean_profit_blacklist()
                        self.last_blacklist_check_time = current_time_str

                    # 检查是否在开市前
                    market_open_time = datetime.strptime('09:30:00', '%H:%M:%S').time()
                    pre_market_time = datetime.strptime('09:15:00', '%H:%M:%S').time()
                    
                    # 检查是否已收市（15:00后）
                    if current_time >= datetime.strptime('15:00:00', '%H:%M:%S').time():
                        if not self.market_closed_hint_shown:
                            self.add_record("交易日结束，市场已收市")
                            self.update_realtime_hint("市场已收市")
                            self.market_closed_hint_shown = True
                        time.sleep(60)  # 收市后每分钟检查一次
                        continue
                    
                    if current_time < market_open_time:
                        # 计算距离开市的时间
                        current_datetime = datetime.combine(datetime.now().date(), current_time)
                        market_open_datetime = datetime.combine(datetime.now().date(), market_open_time)
                        time_delta = market_open_datetime - current_datetime
                        
                        # 计算倒计时信息
                        minutes = time_delta.seconds // 60
                        seconds = time_delta.seconds % 60
                        countdown_msg = f"距离开市还有: {minutes}分{seconds}秒"
                        self.update_realtime_hint(countdown_msg)
                        
                        # 9:15前每分钟更新一次，之后每秒更新
                        if current_time < pre_market_time:
                            time.sleep(60)  # 每分钟更新
                        else:
                            time.sleep(1)   # 每秒更新
                        continue
                    
                    # 原有的监控逻辑
                    current_minute = now.minute
                    current_second = now.second
                    
                    # 取消14:50强制清仓的设定
                    # 原来的强制清仓逻辑已被取消
                    
                    # 检查是否在交易时间（15:00收盘，15:00以后为收盘后）
                    is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') < '11:30:00') or \
                                    ('13:00:00' <= current_time.strftime('%H:%M:%S') < '15:00:00')
                    
                    # 取消14:30后不买入的限制
                    is_after_1430 = False
                    
                    if not is_trading_time:
                        # 如果在午休时间，显示下午开市倒计时
                        if ('11:30:00' <= current_time.strftime('%H:%M:%S') <= '13:00:00'):
                            afternoon_open_time = datetime.strptime('13:00:00', '%H:%M:%S').time()
                            current_datetime = datetime.combine(datetime.now().date(), current_time)
                            afternoon_open_datetime = datetime.combine(datetime.now().date(), afternoon_open_time)
                            time_delta = afternoon_open_datetime - current_datetime
                            
                            minutes = time_delta.seconds // 60
                            seconds = time_delta.seconds % 60
                            countdown_msg = f"距离下午开市还有: {minutes}分{seconds}秒"
                            self.update_realtime_hint(countdown_msg)
                            
                            # 12:45前每分钟更新一次，之后每秒更新
                            if current_time < datetime.strptime('12:45:00', '%H:%M:%S').time():
                                time.sleep(60)  # 每分钟更新
                            else:
                                time.sleep(1)   # 每秒更新
                            continue
                        
                        time.sleep(1)  # 非交易时间每秒检查一次
                        continue
                    
                    # 在交易时间，每5分钟的05秒进行检查
                    if (current_second == 5 and
                        current_minute % 5 == 0 and
                        current_minute != self.last_check_minute):

                        print(f"执行检查 - 当前时间: {current_time.strftime('%H:%M:%S')}")  # 调试输出
                        self.add_record(f"进行5分钟买卖点判定... 时间: {current_time.strftime('%H:%M:%S')}")

                        # 检查是否是9:30的第一次检查，如果是则跳过买入
                        skip_buy_930 = (current_time_str == '09:30' and not self.first_930_check_done)
                        if skip_buy_930:
                            self.add_record("⏰ 9:30第一次定时检查，跳过买入操作")
                            self.check_trading_signals(is_after_1430=is_after_1430, skip_buy=True)
                            self.first_930_check_done = True
                        else:
                            self.check_trading_signals(is_after_1430=is_after_1430)

                        # 更新上次检查时间
                        self.last_check_minute = current_minute
                    
                    time.sleep(0.2)  # 缩短检查间隔，提高精确度
                    
                except Exception as e:
                    self.add_record(f"监控股票时出错: {str(e)}")
                    time.sleep(10)
                
        except Exception as e:
            self.add_record(f"监控股票时出错: {str(e)}")
            time.sleep(10)

    def is_trading_time(self, current_time):
        """判断当前是否在交易时间（15:00收盘，15:00以后为收盘后）"""
        time_str = current_time.strftime('%H:%M:%S')
        return (('09:30:00' <= time_str < '11:30:00') or
                ('13:00:00' <= time_str < '15:00:00'))

    def check_limit_up_status(self, code, current_price):
        """
        检查股票是否涨停

        Args:
            code: 股票代码
            current_price: 当前价格

        Returns:
            tuple: (是否涨停, 涨停状态描述)
        """
        try:
            # 获取当日日线数据来计算涨幅
            today = datetime.now().strftime('%Y%m%d')

            # 先尝试获取实时tick数据
            try:
                tick_data = xtdata.get_full_tick([code])
                if tick_data and code in tick_data:
                    tick = tick_data[code]
                    last_price = tick.get('lastPrice', 0)
                    last_close = tick.get('lastClose', 0)  # 前收盘价

                    if last_close and last_close > 0 and last_price > 0:
                        price_change_ratio = (last_price / last_close - 1) * 100

                        # 判断是否涨停（涨幅在20%上下0.5%范围内）
                        if 19.5 <= price_change_ratio <= 20.5:
                            return True, f"涨幅{price_change_ratio:.2f}%"
                        else:
                            return False, f"涨幅{price_change_ratio:.2f}%"
            except Exception as tick_e:
                print(f"获取{code}实时数据失败: {str(tick_e)}，尝试使用日线数据")

            # 如果实时数据获取失败，使用日线数据
            try:
                # 获取最近2个交易日的日线数据（今天和昨天）
                daily_data = xtdata.get_market_data(
                    field_list=['time', 'open', 'close', 'high', 'low'],
                    stock_list=[code],
                    period='1d',
                    start_time='',
                    end_time='',
                    count=2  # 获取最近2天数据
                )

                if daily_data and code in daily_data['close'].index and len(daily_data['close'].loc[code]) >= 2:
                    closes = daily_data['close'].loc[code]

                    # 获取今日收盘价（最新价）和昨日收盘价
                    today_close = closes.iloc[-1]  # 今日收盘价（或当前价）
                    yesterday_close = closes.iloc[-2]  # 昨日收盘价

                    if yesterday_close > 0:
                        price_change_ratio = (today_close / yesterday_close - 1) * 100

                        # 判断是否涨停（涨幅在20%上下0.5%范围内）
                        if 19.5 <= price_change_ratio <= 20.5:
                            return True, f"涨幅{price_change_ratio:.2f}%"
                        else:
                            return False, f"涨幅{price_change_ratio:.2f}%"
                    else:
                        return False, "昨日收盘价无效"
                else:
                    return False, "无法获取日线数据"

            except Exception as daily_e:
                print(f"获取{code}日线数据失败: {str(daily_e)}")
                return False, f"获取日线数据失败: {str(daily_e)}"

        except Exception as e:
            self.add_record(f"检查{code}涨停状态时出错: {str(e)}")
            return False, f"检查出错: {str(e)}"

    def check_trading_signals(self, is_after_1430=False, skip_buy=False):
        try:
            # 获取当前时间
            now = datetime.now()
            today = now.strftime('%Y%m%d')
            current_time = now.time()
            current_hour = now.hour
            current_minute = now.minute
            
            # 判断当前是否在交易时间
            is_trading_time = self.is_trading_time(current_time)

            # 判断当前是否在收盘时段
            is_noon_break = datetime.strptime('11:30:01', '%H:%M:%S').time() <= current_time <= datetime.strptime('13:04:59', '%H:%M:%S').time()
            is_market_closed = current_time >= datetime.strptime('15:00:00', '%H:%M:%S').time()
            
            # 使用可转债代码作为监控列表（用于买入和卖出判断）
            monitor_codes = self.bond_codes

            # 获取不在股票池但在持仓中的股票（只进行卖出判断）
            position_only_codes = []
            for code in self.position_records.keys():
                if code not in monitor_codes and (code.startswith(('11', '12', '15', '5'))):
                    position_only_codes.append(code)

            # 合并所有需要检查的股票代码
            all_check_codes = list(set(monitor_codes + position_only_codes))

            total_monitor_codes = len(monitor_codes)
            total_position_only = len(position_only_codes)
            total_all_codes = len(all_check_codes)

            self.add_record(f"开始检查买卖信号: 股票池{total_monitor_codes}只(买入+卖出), 持仓独有{total_position_only}只(仅卖出), 总计{total_all_codes}只")

            # 计算5个交易日前的日期
            start_date = (datetime.now() - timedelta(days=5)).strftime('%Y%m%d')

            # 批量获取5分钟数据（包含所有需要检查的股票）
            self.add_record(f"开始下载数据，时间范围: {start_date} 至 {today}")
            self.downloader.my_download(all_check_codes, '5m', start_date, today)
            
            # 记录成功获取数据的股票
            processed_codes = set()  # 使用set避免重复计数
            failed_codes = []
            
            # 遍历所有需要检查的股票
            for i, code in enumerate(all_check_codes):
                # 判断当前股票是否在股票池中（决定是否进行买入判断）
                is_in_stock_pool = code in monitor_codes
                try:
                    # 获取最新数据
                    minute_data = xtdata.get_market_data(
                        field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                        stock_list=[code],
                        period='5m',
                        start_time=start_date,
                        end_time=today,
                        count=-1
                    )
                    
                    # 检查数据是否有效
                    if not minute_data or code not in minute_data['time'].index or len(minute_data['time'].loc[code]) == 0:
                        print(f"[调试] {code} minute_data为空或无数据")
                        self.add_record(f"{code} 数据获取失败")
                        failed_codes.append(code)
                        continue
                    
                    # 创建DataFrame
                    df = pd.DataFrame({
                        'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai'),
                        'open': minute_data['open'].loc[code],
                        'close': minute_data['close'].loc[code],
                        'high': minute_data['high'].loc[code],
                        'low': minute_data['low'].loc[code],
                        'volume': minute_data['volume'].loc[code],
                        'amount': minute_data['amount'].loc[code]
                    })
                    
                    # 使用索引方式获取K线数据（避免时间匹配失败）
                    if len(df) < 3:
                        if i < 3:  # 只为前3只股票显示调试信息
                            self.add_record(f"⚠️ {code} 数据不足，总共只有{len(df)}根K线，跳过判断")
                        # 数据下载成功但不足以计算，仍算作成功处理
                        processed_codes.add(code)
                        continue

                    # 去掉最后一根可能未完成的K线，使用完整的K线数据
                    completed_df = df.iloc[:-1].copy()

                    if len(completed_df) < 2:
                        if i < 3:  # 只为前3只股票显示调试信息
                            self.add_record(f"⚠️ {code} 完整K线数据不足，只有{len(completed_df)}根完整K线，跳过判断")
                        # 数据下载成功但不足以计算，仍算作成功处理
                        processed_codes.add(code)
                        continue

                    # 使用索引方式获取最后两根完整的K线
                    current_kline_data = completed_df.iloc[[-1]]  # 最后一根完整K线
                    prev_kline_data = completed_df.iloc[[-2]]     # 倒数第二根完整K线

                    if len(current_kline_data) == 0 or len(prev_kline_data) == 0:
                        print(f"[调试] {code} current_kline_data或prev_kline_data为空")
                        self.add_record(f"⚠️ {code} K线数据异常，跳过判断")
                        # 数据下载成功但异常，仍算作成功处理
                        processed_codes.add(code)
                        continue
                    
                    # 计算技术指标
                    df = self.calculate_indicators(df)
                    if df is None:
                        print(f"[调试] {code} 指标计算失败，df为None")
                        self.add_record(f"{code} 指标计算失败")
                        failed_codes.append(code)
                        continue

                    # 数据下载和指标计算成功，标记为成功处理
                    # 后续的买卖判断结果不影响成功状态（可能是正常的业务逻辑）
                    processed_codes.add(code)

                    # 获取最新价格
                    current_price = current_kline_data['close'].iloc[-1]
                    
                    # 检查买入信号（包括股票池中的股票和技术指标卖出等待重新买回的股票）
                    if is_in_stock_pool or code in self.tech_sell_waiting_rebuy:
                        # 检查买入信号
                        has_buy_signal, buy_cross_type, _, _ = self.check_cross_signals(df, current_price, code)
                    else:
                        # 不在股票池中且不在技术指标卖出等待重新买回列表中的持仓股票，跳过买入判断
                        # 修复：对所有持仓股票都显示此信息
                        if code in self.position_records:  # 只对持仓股票显示详细信息
                            self.add_record(f"💡 {code} 不在股票池中，跳过买入判断，仅进行卖出判断")
                        has_buy_signal = False

                    # 处理买入信号
                    if has_buy_signal:
                        if buy_cross_type == 'golden':
                            # CCI上穿-100的首次买入信号 - 仅作记录，不实际买入
                            # 检查是否已经持有该股票
                            if code in self.position_records:
                                self.add_record(f"❌ {code} 同一信号，不能重复记录")
                            elif code in self.profit_blacklist:
                                self.add_record(f"🚫 {code} 在获利黑名单中，跳过记录")
                            elif skip_buy:
                                self.add_record(f"⏰ {code} CCI上穿-100买入信号，但9:30第一次检查跳过记录: 价格({current_price:.3f})")
                            else:
                                signal_text = f"🚀 {code} CCI上穿-100买入信号（仅记录，不实际买入）: 价格({current_price:.3f})"
                                # 创建CCI信号记录，作为后续加仓买入的标志
                                self.create_signal_record(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                                self.add_record(signal_text)
                                self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))

                        elif buy_cross_type == 'exp3_break':
                            # 股价上穿EXP3的买入信号
                            if code in self.tech_sell_waiting_rebuy:
                                # 技术指标卖出后重新买回
                                if code in self.profit_blacklist:
                                    self.add_record(f"🚫 {code} 在获利黑名单中，跳过重新买回")
                                elif skip_buy:
                                    self.add_record(f"⏰ {code} 技术指标卖出重新买回信号，但9:30第一次检查跳过买入: 价格({current_price:.3f})")
                                else:
                                    signal_text = f"🔄 {code} 技术指标卖出重新买回信号: 价格({current_price:.3f})"
                                    if is_trading_time:
                                        # 交易时间：执行重新买回操作
                                        self.execute_tech_rebuy(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                                        self.add_record(signal_text)
                                    else:
                                        # 非交易时间：只显示信号，不执行操作
                                        self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                                    self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))
                            elif code not in self.position_records:
                                self.add_record(f"❌ {code} 无持仓记录，不能执行EXP3加仓买入")
                            elif code in self.profit_blacklist:
                                self.add_record(f"🚫 {code} 在获利黑名单中，跳过加仓买入")
                            elif skip_buy:
                                self.add_record(f"⏰ {code} 股价上穿EXP3加仓买入信号，但9:30第一次检查跳过买入: 价格({current_price:.3f})")
                            else:
                                # 检查买入次数限制
                                existing_position = self.position_records.get(code, {})
                                buy_count = existing_position.get('add_count', 0) + 1  # 包括首次买入

                                if buy_count >= 2:
                                    self.add_record(f"❌ {code} 已达到最大买入次数限制(2次)，跳过EXP3加仓买入")
                                else:
                                    # 正常加仓买入信号 - 买入2个单位
                                    signal_text = f"📈 {code} 股价上穿EXP3加仓买入信号（买入2个单位）: 价格({current_price:.3f})"
                                    if is_trading_time:
                                        # 交易时间：执行加仓买入操作，买入2个单位
                                        self.execute_double_buy(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                                        self.add_record(signal_text)
                                    else:
                                        # 非交易时间：只显示信号，不执行操作
                                        self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                                    self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))

                    # 检查持仓股票的卖出条件（优化：先判断盈亏状态）
                    if code in self.position_records:
                        position_info = self.position_records[code]
                        buy_price = position_info['buy_price']
                        quantity = position_info.get('quantity', 10)

                        # 跳过CCI信号记录的卖出检查
                        if quantity == 0 or position_info.get('is_signal_only', False):
                            continue  # CCI信号记录不进行卖出检查

                        current_profit = (current_price - buy_price) * quantity

                        # 安全计算盈利率，避免除零错误
                        if buy_price > 0:
                            profit_rate = (current_price - buy_price) / buy_price * 100
                        else:
                            profit_rate = 0.0

                        # 第一步：根据盈亏额判断止盈止损
                        if current_profit <= -200:
                            # 达到止损条件，执行止损卖出
                            signal_text = f"🔻 {code} 触发止损(-200元): 当前价格{current_price:.3f}，买入价格{buy_price:.3f}，亏损{current_profit:.2f}元"
                            if is_trading_time:
                                # 交易时间：执行止损卖出
                                self.execute_sell(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'stop_loss')
                                self.add_record(signal_text)
                            else:
                                # 非交易时间：只显示信号
                                self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                            continue  # 已处理止损，跳过后续判断
                        elif current_profit <= 0:
                            # 亏损但未达到止损条件，跳过所有其他卖出条件检查
                            # 修复：对所有持仓股票都显示止损判定信息
                            if code in self.position_records:  # 只对持仓股票显示详细信息
                                self.add_record(f"💡 {code} 当前亏损{current_profit:.2f}元({profit_rate:.2f}%)，未达止损条件，跳过其他卖出检查")
                            continue  # 跳过后续所有判断

                        # 进行技术指标判断和止盈检查（不考虑盈亏状态）
                        self.add_record(f"📊 {code} 当前盈亏{current_profit:.2f}元({profit_rate:.2f}%)，进行卖出条件判断")

                        # 首先检查分批止盈策略（优先级最高）
                        profit_state = position_info.get('profit_state', {
                            'first_profit_sell_done': False,   # 是否已执行400元止盈
                            'second_profit_sell_done': False   # 是否已执行700元止盈
                        })

                        # 检查是否涨停，如果涨停则不进行止盈卖出
                        is_limit_up, limit_status = self.check_limit_up_status(code, current_price)

                        # 第一次止盈：盈利达到400元，卖出一个单位
                        if current_profit >= 400 and not profit_state['first_profit_sell_done']:
                            if is_limit_up:
                                self.add_record(f"🚫 {code} 触发第一次止盈(400元)但当前涨停({limit_status})，暂不卖出: 当前价格{current_price:.3f}，盈利{current_profit:.2f}元")
                                continue

                            signal_text = f"💰 {code} 触发第一次止盈(400元): 当前价格{current_price:.3f}，买入价格{buy_price:.3f}，盈利{current_profit:.2f}元，卖出一个单位"
                            if is_trading_time:
                                # 交易时间：执行第一次止盈，卖出一个单位
                                self.execute_sell(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'half_sell')
                                profit_state['first_profit_sell_done'] = True
                                position_info['profit_state'] = profit_state
                                self.add_record(signal_text)
                            else:
                                # 非交易时间：只显示信号
                                self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                            continue

                        # 第二次止盈：盈利达到700元，全部卖出
                        elif current_profit >= 700 and not profit_state['second_profit_sell_done']:
                            if is_limit_up:
                                self.add_record(f"🚫 {code} 触发第二次止盈(700元)但当前涨停({limit_status})，暂不卖出: 当前价格{current_price:.3f}，盈利{current_profit:.2f}元")
                                continue

                            signal_text = f"💰 {code} 触发第二次止盈(700元): 当前价格{current_price:.3f}，买入价格{buy_price:.3f}，盈利{current_profit:.2f}元，全部卖出"
                            if is_trading_time:
                                # 交易时间：执行第二次止盈，全部卖出
                                self.execute_sell(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'full_sell')
                                profit_state['second_profit_sell_done'] = True
                                position_info['profit_state'] = profit_state
                                self.add_record(signal_text)
                            else:
                                # 非交易时间：只显示信号
                                self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                            continue

                        # 然后检查技术指标卖出信号（不考虑盈亏状态）
                        has_sell_signal, sell_cross_type, _, _ = self.check_tech_sell_signals(df, current_price, code)

                        if has_sell_signal:
                            if sell_cross_type == 'first_exp3_break':
                                signal_text = f"📉 {code} 第一次跌破EXP3，卖出一半: 价格({current_price:.3f})"
                                sell_type = 'half_sell'
                            elif sell_cross_type == 'second_exp3_break':
                                signal_text = f"📉 {code} 再次跌破EXP3，卖出剩余: 价格({current_price:.3f})"
                                sell_type = 'full_sell'
                            elif sell_cross_type == 'second_period_no_recovery':
                                signal_text = f"📉 {code} 第二周期未收回，全部卖出: 价格({current_price:.3f})"
                                sell_type = 'full_sell'
                            else:
                                signal_text = f"{code} 出现卖出信号: 价格({current_price:.3f})"
                                sell_type = 'normal'

                            if is_trading_time:
                                # 交易时间：执行技术指标卖出操作
                                self.execute_tech_sell(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), sell_type, current_profit)
                                self.add_record(signal_text)
                            else:
                                # 非交易时间：只显示信号，不执行操作
                                self.add_record(f"[非交易时间] {signal_text} - 仅显示信号")
                            self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))
                            continue  # 已处理卖出信号




                    

                
                except Exception as e:
                    print(f"[调试] 检查 {code} 信号时出错: {str(e)}")
                    self.add_record(f"检查 {code} 信号时出错: {str(e)}")
                    failed_codes.append(code)
            
            # 输出处理结果统计
            processed_count = len(processed_codes)
            failed_count = len(failed_codes)

            self.add_record(f"信号检查完成: 成功处理 {processed_count}/{total_all_codes} 只股票")

            if failed_count > 0:
                self.add_record(f"数据获取失败的股票: {', '.join(failed_codes)}")
                # 如果数据获取失败数量超过10%，记录警告
                if total_all_codes > 0 and failed_count / total_all_codes > 0.1:
                    self.add_record("警告: 数据获取失败的股票数量超过10%，请检查网络连接和数据源")
        
        except Exception as e:
            print(f"[调试] 检查交易信号时出错: {str(e)}")
            self.add_record(f"检查交易信号时出错: {str(e)}")

    def send_request(self, request_data):
        """发送请求到委托查询撤单程序"""
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            client.connect(('localhost', 9876))
            client.sendall(json.dumps(request_data).encode('utf-8'))
            client.shutdown(socket.SHUT_WR)
            
            data = b""
            while True:
                chunk = client.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            response = json.loads(data.decode('utf-8'))
            return response
            
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
            return {"status": "error", "message": str(e)}
        finally:
            client.close()

    def execute_buy(self, code, price, time_str, buy_type='initial'):
        """执行买入操作
        Args:
            code: 股票代码
            price: 买入价格
            time_str: 时间字符串
            buy_type: 买入类型 ('initial': 首次买入, 'add_position': 加仓买入)
        """
        try:
            # 检查买入数量限制：每只股票最多买入两个单位
            if code in self.position_records:
                existing_position = self.position_records[code]
                buy_count = existing_position.get('add_count', 0) + 1  # 包括首次买入

                if buy_count >= 2:
                    self.add_record(f"❌ {code} 已达到最大买入次数限制(2次)，取消本次买入")
                    return
                elif buy_count == 1 and buy_type == 'initial':
                    # 已有持仓，不能再首次买入
                    self.add_record(f"❌ {code} 已有持仓，不能执行首次买入")
                    return
                elif buy_count == 1 and buy_type == 'add_position':
                    # 第二次买入（加仓）
                    self.add_record(f"📊 {code} 执行第2次买入（加仓），这是最后一次买入机会")
            else:
                # 新股票首次买入
                if buy_type == 'add_position':
                    self.add_record(f"❌ {code} 无持仓记录，不能执行加仓买入")
                    return

            # 获取当前时间
            now = datetime.now()

            # 根据买入类型确定买入数量（都按6000元目标金额计算）
            target_amount = 6000  # 目标金额
            if price > 0:
                quantity = int((target_amount / price) // 10) * 10
            else:
                quantity = 10  # 默认最小买入数量

            # 确保至少买入10股
            if quantity < 10:
                quantity = 10

            if buy_type == 'add_position':
                self.add_record(f"📈 {code} 执行EXP3加仓买入，目标金额: {target_amount}元，数量: {quantity}股")
            else:
                self.add_record(f"🚀 {code} 执行CCI首次买入，目标金额: {target_amount}元，数量: {quantity}股")
            
            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20
            
            # 记录原始价格（未加价）用于计算盈亏
            original_price = price
            
            # 买入价格加价（仅用于委托）
            buy_price = price + price_adjust
            
            # 规范化股票代码格式
            code = code.upper()
            
            # CCI策略不需要计算EXP3，直接记录买入信息
            
            # 获取当前5分钟周期
            current_time = datetime.now()
            current_minute = current_time.minute
            current_period = current_minute // 5
            
            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price  # 委托时使用加价后的价格
                    }
                }
                
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"买入委托失败 {code}: {response.get('message', '未知错误')}")
                    return
                
                order_id = response['data']['order_id']
                self.add_record(f"买入委托已提交 {code} 数量:{quantity}股 委托号:{order_id} 价格:{buy_price:.2f}")
                
                # 存储买入指令
                self.trade_instructions[code] = {
                    'type': 'buy',
                    'order_id': order_id,
                    'time': time_str,
                    'price': buy_price,
                    'original_price': original_price  # 记录原始价格
                }
            else:
                order_id = f"VIRTUAL_{int(time.time())}"
                self.add_record(f"[虚拟]买入委托 {code} 数量:{quantity}股 价格:{buy_price:.2f}")
            
            # 记录买入信息，使用原始价格计算成本和盈亏
            if buy_type == 'add_position' and code in self.position_records:
                # 加仓买入：更新现有持仓记录
                existing_position = self.position_records[code]
                existing_quantity = existing_position['quantity']
                existing_cost = existing_position['buy_price'] * existing_quantity
                existing_fee = existing_position['fee']

                # 计算新的平均成本
                new_total_quantity = existing_quantity + quantity
                new_total_cost = existing_cost + (original_price * quantity)
                if new_total_quantity > 0:
                    new_avg_price = new_total_cost / new_total_quantity
                else:
                    new_avg_price = original_price  # 防止除零错误
                new_total_fee = existing_fee + (original_price * quantity * 0.0001)

                # 更新持仓记录
                self.position_records[code].update({
                    'buy_price': float(new_avg_price),  # 更新为平均成本
                    'quantity': int(new_total_quantity),
                    'fee': float(new_total_fee),
                    'actual_amount': float(new_total_cost),
                    'last_add_time': time_str,  # 记录最后加仓时间
                    'add_count': existing_position.get('add_count', 0) + 1  # 加仓次数
                })

                self.add_record(f"📈 {code} 加仓完成: 原持仓{existing_quantity}股@{existing_position['buy_price']:.3f}, 加仓{quantity}股@{original_price:.3f}, 新持仓{new_total_quantity}股@{new_avg_price:.3f}")
            else:
                # 首次买入：创建新的持仓记录
                self.position_records[code] = {
                    'buy_price': float(original_price),  # 使用原始价格
                    'order_price': float(buy_price),     # 记录委托价格
                    'buy_time': time_str,
                    'quantity': int(quantity),
                    'fee': float(original_price * quantity * 0.0001),  # 使用原始价格计算手续费
                    'actual_amount': float(original_price * quantity),  # 使用原始价格计算实际金额
                    'order_id': order_id,
                    'virtual': bool(not self.trading_enabled),
                    'buy_period': current_period,
                    'buy_hour': now.hour,
                    'periods_since_buy': 0,
                    'profit_check_done': False,
                    'add_count': 0  # 加仓次数
                }
            
            # 更新持仓列表显示
            self.update_position_list()
            
            # 保存交易数据
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"买入 {code} 失败: {str(e)}")

    def create_signal_record(self, code, price, time_str):
        """创建CCI信号记录（CCI首次买入信号时使用）"""
        try:
            # 获取当前时间信息
            now = datetime.now()
            current_minute = now.minute
            current_period = current_minute // 5

            # 创建CCI信号记录，不是真正的持仓
            self.position_records[code] = {
                'buy_price': float(price),
                'order_price': float(price),
                'buy_time': time_str,
                'quantity': 0,  # 信号记录数量为0
                'fee': 0.0,
                'actual_amount': 0.0,
                'order_id': f"CCI_SIGNAL_{int(time.time())}",
                'virtual': bool(not self.trading_enabled),  # 保持系统级虚拟交易标记
                'buy_period': current_period,
                'buy_hour': now.hour,
                'periods_since_buy': 0,
                'profit_check_done': False,
                'add_count': 0,  # 加仓次数
                'is_cci_signal': True,  # 标记为CCI信号产生的记录
                'is_signal_only': True  # 标记为仅信号记录，不是实际持仓
            }

            # 更新持仓列表显示
            self.update_position_list()

            # 保存交易数据
            self.save_trading_data()

            self.add_record(f"📝 {code} 创建CCI信号记录，等待EXP3突破信号进行实际买入")

        except Exception as e:
            self.add_record(f"创建CCI信号记录 {code} 失败: {str(e)}")

    def execute_double_buy(self, code, price, time_str):
        """执行双倍买入操作（EXP3突破时买入2个单位）"""
        try:
            # 检查是否有CCI信号记录
            if code not in self.position_records:
                self.add_record(f"❌ {code} 无CCI信号记录，不能执行双倍买入")
                return

            position_info = self.position_records[code]
            if not position_info.get('is_signal_only', False):
                self.add_record(f"❌ {code} 不是CCI信号记录，不能执行双倍买入")
                return

            # 计算买入数量：2个单位，每单位6000元
            target_amount_per_unit = 6000
            total_target_amount = target_amount_per_unit * 2  # 2个单位

            if price > 0:
                quantity = int((total_target_amount / price) // 10) * 10
            else:
                quantity = 20  # 默认最小买入数量（2个单位）

            # 确保至少买入20股（2个单位最少）
            if quantity < 20:
                quantity = 20

            self.add_record(f"📈 {code} 执行EXP3双倍买入，目标金额: {total_target_amount}元（2个单位），数量: {quantity}股")

            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20

            # 记录原始价格（未加价）用于计算盈亏
            original_price = price

            # 买入价格加价（仅用于委托）
            buy_price = price + price_adjust

            # 规范化股票代码格式
            code = code.upper()

            # 获取当前时间信息
            now = datetime.now()
            current_minute = now.minute
            current_period = current_minute // 5

            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price  # 委托时使用加价后的价格
                    }
                }

                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"双倍买入委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                order_id = response['data']['order_id']
                self.add_record(f"双倍买入委托已提交 {code} 数量:{quantity}股 委托号:{order_id} 价格:{buy_price:.2f}")

                # 存储买入指令
                self.trade_instructions[code] = {
                    'type': 'buy',
                    'order_id': order_id,
                    'time': time_str,
                    'price': buy_price,
                    'original_price': original_price  # 记录原始价格
                }
            else:
                order_id = f"VIRTUAL_DOUBLE_{int(time.time())}"
                self.add_record(f"[虚拟]双倍买入委托 {code} 数量:{quantity}股 价格:{buy_price:.2f}")

            # 更新持仓记录，将CCI信号记录转换为实际持仓
            self.position_records[code].update({
                'buy_price': float(original_price),  # 使用原始价格
                'order_price': float(buy_price),     # 记录委托价格
                'buy_time': time_str,
                'quantity': int(quantity),
                'fee': float(original_price * quantity * 0.0001),  # 使用原始价格计算手续费
                'actual_amount': float(original_price * quantity),  # 使用原始价格计算实际金额
                'order_id': order_id,
                'virtual': bool(not self.trading_enabled),  # 保持系统级虚拟交易标记
                'buy_period': current_period,
                'buy_hour': now.hour,
                'periods_since_buy': 0,
                'profit_check_done': False,
                'add_count': 1,  # 标记为已加仓1次（实际是首次实际买入）
                'is_double_buy': True,  # 标记为双倍买入
                'is_signal_only': False,  # 转换为实际持仓
                'is_cci_signal': False  # 不再是纯信号记录
            })

            # 更新持仓列表显示
            self.update_position_list()

            # 保存交易数据
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"双倍买入 {code} 失败: {str(e)}")

    def execute_temp_sell(self, code, price, time_str):
        """执行暂时卖出操作（实际买入后股价跌破EXP3时）"""
        try:
            if code not in self.position_records:
                self.add_record(f"❌ {code} 无持仓记录，不能执行暂时卖出")
                return

            position_info = self.position_records[code]
            buy_price = position_info['buy_price']
            total_quantity = position_info.get('quantity', 10)

            # 检查是否为CCI信号记录
            if position_info.get('is_signal_only', False):
                self.add_record(f"❌ {code} 为CCI信号记录，不能执行暂时卖出")
                return

            self.add_record(f"⚠️ {code} 执行暂时卖出，数量: {total_quantity}股，买入价: {buy_price:.3f}，当前价: {price:.3f}")

            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40

            # 卖出价格降价
            sell_price = price - price_adjust

            # 规范化股票代码格式
            code = code.upper()

            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_SELL,
                        'volume': total_quantity,
                        'price_type': FIX_PRICE,
                        'price': sell_price
                    }
                }

                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"暂时卖出委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                order_id = response['data']['order_id']
                self.add_record(f"暂时卖出委托已提交 {code} 数量:{total_quantity}股 委托号:{order_id} 价格:{sell_price:.2f}")
            else:
                order_id = f"VIRTUAL_TEMP_SELL_{int(time.time())}"
                self.add_record(f"[虚拟]暂时卖出委托 {code} 数量:{total_quantity}股 价格:{sell_price:.2f}")

            # 保存暂时卖出信息到等待重新买回字典
            self.tech_sell_waiting_rebuy[code] = {
                'original_position': position_info.copy(),  # 保存原始持仓信息
                'temp_sell_price': price,  # 暂时卖出时的价格（未降价）
                'temp_sell_time': time_str,
                'temp_sell_order_id': order_id,
                'virtual': bool(not self.trading_enabled)
            }

            # 从持仓记录中移除（暂时）
            del self.position_records[code]

            # 更新持仓列表显示
            self.update_position_list()

            # 保存交易数据
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"暂时卖出 {code} 失败: {str(e)}")

    def execute_rebuy(self, code, price, time_str):
        """执行重新买回操作（暂时卖出后股价重新上穿EXP3时）"""
        try:
            if code not in self.tech_sell_waiting_rebuy:
                self.add_record(f"❌ {code} 不在技术指标卖出等待重新买回列表中")
                return

            temp_sell_info = self.tech_sell_waiting_rebuy[code]
            original_position = temp_sell_info['original_position']
            original_quantity = original_position.get('quantity', 10)

            self.add_record(f"🔄 {code} 执行重新买回，数量: {original_quantity}股，原买入价: {original_position['buy_price']:.3f}，当前价: {price:.3f}")

            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20

            # 买入价格加价
            buy_price = price + price_adjust

            # 规范化股票代码格式
            code = code.upper()

            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': original_quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price
                    }
                }

                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"重新买回委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                order_id = response['data']['order_id']
                self.add_record(f"重新买回委托已提交 {code} 数量:{original_quantity}股 委托号:{order_id} 价格:{buy_price:.2f}")
            else:
                order_id = f"VIRTUAL_REBUY_{int(time.time())}"
                self.add_record(f"[虚拟]重新买回委托 {code} 数量:{original_quantity}股 价格:{buy_price:.2f}")

            # 恢复持仓记录，使用原始买入价格
            self.position_records[code] = original_position.copy()
            self.position_records[code].update({
                'rebuy_price': price,  # 记录重新买回价格
                'rebuy_time': time_str,
                'rebuy_order_id': order_id,
                'temp_sell_count': self.position_records[code].get('temp_sell_count', 0) + 1  # 记录暂时卖出次数
            })

            # 从技术指标卖出等待重新买回字典中移除
            del self.tech_sell_waiting_rebuy[code]

            # 更新持仓列表显示
            self.update_position_list()

            # 保存交易数据
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"重新买回 {code} 失败: {str(e)}")

    def execute_tech_rebuy(self, code, price, time_str):
        """执行技术指标卖出后重新买回操作（股价重新上穿EXP3时）"""
        try:
            if code not in self.tech_sell_waiting_rebuy:
                self.add_record(f"❌ {code} 不在技术指标卖出等待重新买回列表中")
                return

            tech_sell_info = self.tech_sell_waiting_rebuy[code]
            original_buy_price = tech_sell_info['original_buy_price']
            original_buy_time = tech_sell_info['original_buy_time']
            sell_price = tech_sell_info['sell_price']
            loss_amount = tech_sell_info['loss_amount']

            self.add_record(f"🔄 {code} 执行技术指标重新买回，原买入价: {original_buy_price:.3f}，卖出价: {sell_price:.3f}，当前价: {price:.3f}，原亏损: {abs(loss_amount):.2f}元")

            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20

            # 买入价格加价
            buy_price = price + price_adjust
            quantity = 10  # 默认买入数量

            # 规范化股票代码格式
            code = code.upper()

            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price
                    }
                }

                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"技术指标重新买回委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                order_id = response['data']['order_id']
                self.add_record(f"技术指标重新买回委托已提交 {code} 数量:{quantity}股 委托号:{order_id} 价格:{buy_price:.2f}")
            else:
                order_id = f"VIRTUAL_TECH_REBUY_{int(time.time())}"
                self.add_record(f"[虚拟]技术指标重新买回委托 {code} 数量:{quantity}股 价格:{buy_price:.2f}")

            # 创建新的持仓记录
            self.position_records[code] = {
                'buy_price': buy_price,
                'buy_time': time_str,
                'quantity': quantity,
                'fee': buy_price * quantity * 0.0003,
                'order_id': order_id,
                'virtual': not self.trading_enabled,
                'actual_amount': buy_price * quantity,
                'tech_rebuy_from_loss': True,  # 标记为技术指标亏损重新买回
                'original_loss': loss_amount,  # 记录原始亏损
                'original_sell_price': sell_price,  # 记录原始卖出价格
                'tech_rebuy_count': 1  # 记录技术指标重新买回次数
            }

            # 从技术指标卖出等待重新买回字典中移除
            del self.tech_sell_waiting_rebuy[code]

            # 更新持仓列表显示
            self.update_position_list()

            # 保存交易数据
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"技术指标重新买回 {code} 失败: {str(e)}")

    def execute_sell(self, code, price, time_str, sell_type='normal'):
        """执行卖出操作
        Args:
            code: 股票代码
            price: 卖出价格
            time_str: 时间字符串
            sell_type: 卖出类型 ('normal': 全部卖出, 'half_sell': 卖出一半, 'full_sell': 卖出剩余)
        """
        try:
            if code not in self.position_records:
                # 如果没有持仓记录但在等待盈利卖出列表中，清理它
                if code in self.waiting_for_profit_sell:
                    del self.waiting_for_profit_sell[code]
                return

            # 获取持仓信息
            position_info = self.position_records[code]
            buy_price = position_info['buy_price']

            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40

            # 正常卖出，使用降价
            
            # 获取持仓信息
            position_info = self.position_records[code]
            total_quantity = position_info.get('quantity', 10)
            buy_price = position_info['buy_price']
            buy_fee = position_info.get('fee', 0)
            is_virtual = position_info.get('virtual', False)

            # 根据卖出类型确定卖出数量
            if sell_type == 'half_sell':
                # 卖出一半，向下取整到10的倍数
                quantity = int(total_quantity // 2 // 10) * 10
                if quantity < 10:  # 确保至少卖出10股
                    quantity = 10
                self.add_record(f"📉 {code} 分批卖出：卖出一半 {quantity}/{total_quantity} 股")
            elif sell_type == 'full_sell':
                # 卖出剩余全部
                quantity = total_quantity
                self.add_record(f"📉 {code} 分批卖出：卖出剩余全部 {quantity} 股")
            else:
                # 正常卖出全部
                quantity = total_quantity
                self.add_record(f"📊 {code} 正常卖出：全部 {quantity} 股")
            
            if self.trading_enabled and not is_virtual:
                # 先查询实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                    return
                
                # 检查是否有足够的持仓
                position_found = False
                actual_volume = 0
                for position in response['data']:
                    if position['stock_code'] == code:
                        position_found = True
                        actual_volume = position['volume']
                        break
                        
                if not position_found:
                    self.add_record(f"未找到 {code} 的实际持仓,取消卖出")
                    # 清理所有相关记录
                    if code in self.position_records:
                        del self.position_records[code]
                    if code in self.waiting_for_profit_sell:
                        del self.waiting_for_profit_sell[code]
                    if code in self.trade_instructions:
                        del self.trade_instructions[code]
                    self.update_position_list()
                    self.save_trading_data()
                    return
                
                # 如果实际持仓小于要卖出的数量,调整卖出数量
                if actual_volume < quantity:
                    self.add_record(f"{code} 实际持仓({actual_volume})小于记录持仓({quantity}),调整卖出数量")
                    quantity = actual_volume
                    if quantity == 0:
                        self.add_record(f"{code} 无可卖出持仓,取消卖出")
                        # 清理所有相关记录
                        if code in self.position_records:
                            del self.position_records[code]
                        if code in self.waiting_for_profit_sell:
                            del self.waiting_for_profit_sell[code]
                        if code in self.trade_instructions:
                            del self.trade_instructions[code]
                        self.update_position_list()
                        self.save_trading_data()
                        return
                
                # 发送卖出委托请求
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_SELL,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': price - price_adjust
                    }
                }
                
                # 发送委托请求
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"卖出委托失败 {code}: {response.get('message', '未知错误')}")
                    return
                
                # 获取委托号
                order_id = response['data']['order_id']
                self.add_record(f"卖出委托已提交 {code} 委托号: {order_id}")
                
                # 记录卖出委托号到持仓记录
                self.position_records[code]['sell_order_id'] = order_id
                
                # 尝试获取实际成交价格（一段时间后再查询）
                def check_sell_trade_report():
                    # 延迟5秒后查询成交回报
                    time.sleep(5)
                    try:
                        # 查询该委托的成交情况
                        real_sell_price = None
                        
                        # 查询当日成交
                        request = {
                            'type': 'query_trades',
                            'params': {
                                'start_date': datetime.now().strftime('%Y%m%d'),
                                'end_date': datetime.now().strftime('%Y%m%d'),
                                'order_id': order_id
                            }
                        }
                        response = self.send_request(request)
                        
                        if response['status'] == 'success' and response['data']:
                            for trade in response['data']:
                                if trade['direction'] == STOCK_SELL and trade['order_id'] == order_id:
                                    real_sell_price = trade['price']
                                    self.add_record(f"获取到 {code} 的实际卖出价格: {real_sell_price:.3f}")
                                    break
                        
                        # 如果找到实际成交价格，更新交易记录
                        if real_sell_price:
                            for record in self.trade_records:
                                if record.get('sell_order_id') == order_id:
                                    original_price = record['sell_price']
                                    record['sell_price'] = real_sell_price
                                    record['profit'] = round((real_sell_price * quantity) - 
                                                          record.get('actual_amount', buy_price * quantity) - 
                                                          record.get('total_fee', 0), 2)
                                    record['profit_percent'] = round(record['profit'] / 
                                                                  record.get('actual_amount', buy_price * quantity) * 100, 2)
                                    
                                    # 更新当日总盈亏
                                    self.daily_total_profit = round(self.daily_total_profit + record['profit'], 2)
                                    
                                    self.add_record(f"已更新 {code} 的卖出交易记录价格: {original_price:.3f} -> {real_sell_price:.3f}")
                                    self.save_trading_data()
                                    break
                    except Exception as e:
                        self.add_record(f"查询卖出成交回报失败: {str(e)}")
                
                # 启动线程等待并查询成交回报
                threading.Thread(target=check_sell_trade_report, daemon=True).start()
                
            else:
                # 虚拟卖出
                order_id = f"VIRTUAL_SELL_{int(time.time())}"
                self.add_record(f"[虚拟]卖出委托 {code} 价格: {price:.2f}")
            
            # 计算卖出金额和手续费
            sell_amount = price * quantity
            sell_fee = sell_amount * 0.0001

            # 分批卖出时需要按比例计算买入手续费
            if sell_type == 'half_sell' and total_quantity > 0:
                proportional_buy_fee = buy_fee * (quantity / total_quantity)
            else:
                proportional_buy_fee = buy_fee

            total_fee = proportional_buy_fee + sell_fee

            # 计算盈亏（按比例计算成本）
            proportional_cost = buy_price * quantity
            profit = sell_amount - proportional_cost - total_fee

            # 安全计算盈亏百分比，避免除零错误
            if proportional_cost > 0:
                profit_percent = (profit / proportional_cost) * 100
            else:
                profit_percent = 0.0
            
            # 只在非实盘交易时更新当日总盈亏（实盘交易在成交回报中更新）
            if not self.trading_enabled or is_virtual:
                self.daily_total_profit = round(self.daily_total_profit + profit, 2)
            
            # 显示卖出信息
            virtual_tag = "[虚拟]" if is_virtual else ""
            sell_record = f"{virtual_tag}卖出 {code} 价格:{price:.2f} 数量:{quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%)"
            self.add_record(sell_record)
            
            # 记录完整交易信息到交易历史
            self.trade_records.append({
                'code': code,
                'buy_price': buy_price,
                'buy_time': position_info['buy_time'],
                'sell_price': price,
                'sell_time': time_str,
                'quantity': quantity,
                'buy_fee': round(proportional_buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': position_info.get('order_id'),
                'sell_order_id': order_id,
                'virtual': is_virtual,
                'actual_amount': proportional_cost,
                'sell_type': sell_type  # 记录卖出类型
            })

            # 如果获利，将股票添加到黑名单
            if profit > 0:
                self.add_to_profit_blacklist(code)

            # 根据卖出类型处理持仓记录
            if sell_type == 'half_sell':
                # 分批卖出一半，更新剩余持仓
                remaining_quantity = total_quantity - quantity
                remaining_fee = buy_fee - proportional_buy_fee
                remaining_amount = position_info.get('actual_amount', buy_price * total_quantity) - proportional_cost

                # 更新持仓记录
                self.position_records[code].update({
                    'quantity': remaining_quantity,
                    'fee': remaining_fee,
                    'actual_amount': remaining_amount
                })

                self.add_record(f"📊 {code} 分批卖出完成，剩余持仓: {remaining_quantity}股")
            else:
                # 全部卖出，清理所有相关记录
                if code in self.position_records:
                    del self.position_records[code]
                if code in self.waiting_for_profit_sell:
                    del self.waiting_for_profit_sell[code]
                if code in self.trade_instructions:
                    del self.trade_instructions[code]

                self.add_record(f"📊 {code} 全部卖出完成，已清理持仓记录")

            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"卖出 {code} 失败: {str(e)}")
            # 发生错误时清理记录
            try:
                if code in self.position_records:
                    del self.position_records[code]
                if code in self.waiting_for_profit_sell:
                    del self.waiting_for_profit_sell[code]
                if code in self.trade_instructions:
                    del self.trade_instructions[code]
                self.update_position_list()
                self.save_trading_data()
            except:
                pass

    def execute_tech_sell(self, code, price, time_str, sell_type='normal', current_profit=0):
        """执行技术指标卖出操作，卖出后根据盈亏情况决定是否加入重新买入队列
        Args:
            code: 股票代码
            price: 卖出价格
            time_str: 时间字符串
            sell_type: 卖出类型 ('normal': 全部卖出, 'half_sell': 卖出一半, 'full_sell': 卖出剩余)
            current_profit: 当前盈亏金额
        """
        try:
            # 先执行正常的卖出操作
            self.execute_sell(code, price, time_str, sell_type)

            # 如果是亏损状态，将股票加入技术指标卖出等待重新买回队列
            if current_profit < 0:
                # 获取原始持仓信息（在卖出前保存）
                if code in self.position_records:
                    position_info = self.position_records[code]
                    buy_price = position_info['buy_price']
                    buy_time = position_info['buy_time']

                    # 加入技术指标卖出等待重新买回队列
                    self.tech_sell_waiting_rebuy[code] = {
                        'sell_price': price,
                        'sell_time': time_str,
                        'original_buy_price': buy_price,
                        'original_buy_time': buy_time,
                        'sell_type': sell_type,
                        'loss_amount': current_profit
                    }

                    self.add_record(f"💔 {code} 技术指标卖出亏损{abs(current_profit):.2f}元，已加入重新买回队列")

                    # 保存到文件
                    self.save_trading_data()
                else:
                    self.add_record(f"⚠️ {code} 技术指标卖出后盈利{current_profit:.2f}元，正常记录")

        except Exception as e:
            self.add_record(f"技术指标卖出 {code} 失败: {str(e)}")

    def execute_protective_sell(self, code, price, time_str, avg_price):
        """执行保护性卖出操作"""
        try:
            if code not in self.position_records:
                return
            
            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40
            
            # 先查询实际持仓
            request = {'type': 'query_positions'}
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                return
            
            # 检查是否有足够的持仓
            position_found = False
            actual_volume = 0
            for position in response['data']:
                if position['stock_code'] == code:
                    position_found = True
                    actual_volume = position['volume']
                    break
            
            if not position_found:
                self.add_record(f"未找到 {code} 的实际持仓,取消保护性卖出")
                # 从持仓记录中移除
                if code in self.position_records:
                    del self.position_records[code]
                self.update_position_list()
                self.save_trading_data()
                return
            
            # 获取本地记录的持仓信息
            buy_info = self.position_records[code]
            quantity = buy_info.get('quantity', 10)
            
            # 如果实际持仓小于要卖出的数量,调整卖出数量
            if actual_volume < quantity:
                self.add_record(f"{code} 实际持仓({actual_volume})小于记录持仓({quantity}),调整保护性卖出数量")
                quantity = actual_volume
                if quantity == 0:
                    self.add_record(f"{code} 无可卖出持仓,取消保护性卖出")
                    # 从持仓记录中移除
                    if code in self.position_records:
                        del self.position_records[code]
                    self.update_position_list()
                    self.save_trading_data()
                    return

            # 其他保护性卖出逻辑保持不变...
            buy_price = buy_info['buy_price']
            buy_fee = buy_info.get('fee', 0)
            
            # 发送卖出委托请求
            request = {
                'type': 'place_order',
                'params': {
                    'stock_code': code,
                    'direction': STOCK_SELL,  # 卖出指令
                    'volume': quantity,
                    'price_type': FIX_PRICE,  # 限价委托
                    'price': price - price_adjust  # 卖出价格降低0.40元提高成交概率
                }
            }
            
            # 发送委托请求
            response = self.send_request(request)
            if response['status'] != 'success':
                self.add_record(f"保护性卖出委托失败 {code}: {response.get('message', '未知错误')}")
                return
            
            # 获取委托号
            order_id = response['data']['order_id']
            self.add_record(f"保护性卖出委托已提交 {code} 委托号: {order_id}")
            
            # 记录卖出委托号到持仓记录
            self.position_records[code]['sell_order_id'] = order_id
            
            # 计算卖出金额和手续费
            sell_amount = price * quantity
            sell_fee = sell_amount * 0.0001
            total_fee = buy_fee + sell_fee
            
            # 计算盈亏
            actual_amount = buy_info.get('actual_amount', buy_price * quantity)
            profit = sell_amount - actual_amount - total_fee

            # 安全计算盈亏百分比，避免除零错误
            if actual_amount > 0:
                profit_percent = (profit / actual_amount) * 100
            else:
                profit_percent = 0.0
            
            # 显示卖出信息（包含保护性卖出原因）
            sell_record = (f"卖出 {code} 价格:{price:.2f} 数量:{quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%) "
                          f"[保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}]")
            self.add_record(sell_record)
            
            # 记录完整交易信息到交易历史
            self.trade_records.append({
                'code': code,
                'buy_price': buy_price,
                'buy_time': buy_info['buy_time'],
                'sell_price': price,
                'sell_time': time_str,
                'quantity': quantity,
                'buy_fee': round(buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': buy_info.get('order_id'),
                'sell_order_id': order_id,
                'reason': f"保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}"
            })
            
            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"保护性卖出 {code} 失败: {str(e)}")
    
    def add_record(self, text):
        """添加记录到记录列表并写入日志文件"""
        try:
            # 添加到GUI界面
            if hasattr(self, 'record_listbox'):
                self._add_record_impl(text)
            else:
                print(f"记录列表未初始化: {text}")

            # 写入日志文件
            self.write_log(text)

        except Exception as e:
            print(f"添加记录失败: {str(e)}")
    
    def _add_record_impl(self, text):
        """实际添加记录的实现"""
        try:
            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            text_with_time = f"{timestamp} {text}"
            
            # 添加到记录列表框
            self.record_listbox.insert(0, text_with_time)  # 新记录添加到顶部
        except Exception as e:
            print(f"添加记录失败: {str(e)}")
    
    def add_trade_record(self, text):
        """添加交易记录"""
        # 添加到通用记录
        self.add_record(text)
    
    def show_position_stock(self, event):
        """显示选中持仓股票的分时图"""
        selection = self.position_listbox.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓股票")
            return

        # 从持仓列表项中提取股票代码
        position_text = self.position_listbox.get(selection[0])
        code = self.extract_code_from_position_text(position_text)

        if not code:
            messagebox.showerror("错误", "无法从持仓信息中提取股票代码")
            return

        # 调用显示分时图的方法
        threading.Thread(target=lambda: self.check_and_show_chart(code)).start()
    
    def check_and_show_chart(self, code):
        """检查股票并显示分时图"""
        try:
            # 检查股票代码是否有效
            is_valid = self.is_valid_for_trading(code)
            if not is_valid:
                # 使用after方法在主线程中显示消息
                self.root.after(0, lambda: messagebox.showinfo("提示", f"{code} 当前不可交易或数据不可用"))
                return
            
            # 获取数据
            today = datetime.now().strftime('%Y%m%d')
            self.downloader.my_download([code], '1m', today, today)
            time.sleep(1)  # 等待数据就绪
            
            # 在主线程中显示图表
            self.root.after(0, lambda: self.display_chart(code))
        except Exception as e:
            error_msg = str(e)
            # 使用after方法在主线程中显示错误
            self.root.after(0, lambda: messagebox.showerror("错误", f"准备显示分时图失败: {error_msg}"))
    
    def update_position_list(self):
        """更新持仓列表显示"""
        try:
            # 清空列表框
            self.position_listbox.delete(0, tk.END)
            
            # 初始化当前持仓计数和浮动盈亏
            current_positions = 0
            total_floating_profit = 0.0
            
            if self.trading_enabled:
                # 查询实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                    return
                
                # 创建一个新的字典来存储更新后的持仓记录
                updated_positions = {}

                # 统计需要更新价格的持仓数量
                valid_positions = [pos for pos in response['data']
                                 if pos['stock_code'].startswith(('11', '12', '15', '5')) and pos['volume'] > 0]
                total_positions = len(valid_positions)

                if total_positions > 0:
                    print(f"📊 开始更新 {total_positions} 只持仓股票的价格...")

                # 遍历服务器返回的持仓数据
                for i, position in enumerate(response['data'], 1):
                    code = position['stock_code']
                    volume = position['volume']
                    
                    # 只处理可转债(11或12开头)和ETF(15或5开头)
                    if not (code.startswith(('11', '12', '15', '5'))):
                        continue
                    
                    if volume <= 0:  # 跳过零持仓
                        continue
                    
                    current_positions += 1  # 增加持仓计数

                    # 显示价格获取进度
                    valid_position_index = sum(1 for pos in response['data'][:i]
                                             if pos['stock_code'].startswith(('11', '12', '15', '5')) and pos['volume'] > 0)
                    if valid_position_index <= total_positions:
                        print(f"📈 ({valid_position_index}/{total_positions}) 获取 {code} 价格...")

                    # 获取当前价格
                    current_price = self.get_current_price(code)
                    if not current_price:
                        print(f"❌ 跳过 {code}，无法获取价格")
                        continue
                    
                    # 从本地记录中获取买入信息，如果没有则创建新记录
                    buy_info = self.position_records.get(code, {})
                    if not buy_info:
                        # 尝试从成交回报获取真实成交价格
                        self.add_record(f"发现新持仓 {code}，尝试从成交回报获取实际成交价格")
                        
                        # 临时创建一个基础记录以便显示
                        buy_info = {
                            'buy_price': current_price,  # 临时使用当前价格，后续会尝试更新
                            'buy_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'quantity': volume,
                            'fee': current_price * volume * 0.0001,  # 手续费万分之一
                            'actual_amount': current_price * volume
                        }
                        
                        # 添加到持仓记录
                        self.position_records[code] = buy_info
                        
                        # 尝试从成交回报获取真实成交价格并更新记录
                        self.process_trade_reports(code)
                        
                        # 重新获取已更新的记录
                        buy_info = self.position_records.get(code, buy_info)
                    
                    # 计算该持仓的浮动盈亏（与显示保持一致，扣除手续费）
                    buy_price = buy_info.get('buy_price')
                    if buy_price is None:
                        self.add_record(f"警告: {code} 的买入价格为空，跳过盈亏计算")
                        continue
                    gross_profit = (current_price - buy_price) * volume

                    # 计算手续费
                    buy_fee = buy_price * volume * 0.0003
                    sell_fee = current_price * volume * 0.0003
                    total_fee = buy_fee + sell_fee

                    # 净浮动盈亏（扣除手续费）
                    floating_profit = gross_profit - total_fee
                    total_floating_profit += floating_profit
                    
                    # 更新单只股票的最大浮盈和最大浮亏
                    if floating_profit > 0:
                        self.single_stock_max_profit = max(self.single_stock_max_profit, floating_profit)
                    else:
                        self.single_stock_max_loss = min(self.single_stock_max_loss, floating_profit)
                    
                    # 只在收盘后缓存价格，交易时间内价格随时变动不应缓存
                    current_time = datetime.now().time()
                    is_trading_time = (
                        (datetime.strptime('09:30:00', '%H:%M:%S').time() <= current_time < datetime.strptime('11:30:00', '%H:%M:%S').time()) or
                        (datetime.strptime('13:00:00', '%H:%M:%S').time() <= current_time < datetime.strptime('15:00:00', '%H:%M:%S').time())
                    )
                    if not is_trading_time:
                        # 收盘后缓存价格，避免重复下载
                        buy_info['current_price'] = current_price

                    # 更新持仓记录
                    updated_positions[code] = buy_info
                    
                    # 显示持仓信息
                    self.display_position(code, volume, current_price, buy_info)
                
                # 更新本地持仓记录
                self.position_records = updated_positions

                # 显示更新完成信息
                if total_positions > 0:
                    successful_updates = len(updated_positions)
                    print(f"✅ 持仓价格更新完成: 成功更新 {successful_updates}/{total_positions} 只股票")


                
            else:
                # 显示虚拟持仓
                # 判断是否在交易时间
                current_time = datetime.now().time()
                is_trading_time = (
                    (datetime.strptime('09:30:00', '%H:%M:%S').time() <= current_time < datetime.strptime('11:30:00', '%H:%M:%S').time()) or
                    (datetime.strptime('13:00:00', '%H:%M:%S').time() <= current_time < datetime.strptime('15:00:00', '%H:%M:%S').time())
                )

                for code, info in self.position_records.items():
                    if info.get('virtual', False):  # 只显示虚拟持仓
                        current_positions += 1  # 增加持仓计数
                        volume = info.get('quantity', 0)

                        if is_trading_time:
                            # 交易时间内，每次都获取最新价格
                            current_price = self.get_current_price(code)
                        else:
                            # 收盘后，优先使用缓存的价格
                            current_price = info.get('current_price')
                            if not current_price:
                                current_price = self.get_current_price(code)
                                if current_price:
                                    # 缓存获取到的价格
                                    self.position_records[code]['current_price'] = current_price

                        if current_price:
                            # 计算该持仓的浮动盈亏（与显示保持一致，扣除手续费）
                            buy_price = info.get('buy_price')
                            if buy_price is None:
                                self.add_record(f"警告: 虚拟持仓 {code} 的买入价格为空，跳过盈亏计算")
                                continue
                            gross_profit = (current_price - buy_price) * volume

                            # 计算手续费
                            buy_fee = buy_price * volume * 0.0003
                            sell_fee = current_price * volume * 0.0003
                            total_fee = buy_fee + sell_fee

                            # 净浮动盈亏（扣除手续费）
                            floating_profit = gross_profit - total_fee
                            total_floating_profit += floating_profit
                            
                            # 更新单只股票的最大浮盈和最大浮亏
                            if floating_profit > 0:
                                self.single_stock_max_profit = max(self.single_stock_max_profit, floating_profit)
                            else:
                                self.single_stock_max_loss = min(self.single_stock_max_loss, floating_profit)
                        
                        self.display_position(code, volume, current_price, info, virtual=True)

            # 显示技术指标卖出等待重新买回的股票
            for code, temp_sell_info in self.tech_sell_waiting_rebuy.items():
                self.display_tech_sell_waiting_rebuy(code, temp_sell_info)

            # 更新最大持仓数
            self.max_positions_today = max(self.max_positions_today, current_positions)
            
            # 更新持仓统计显示
            stats_text = (
                f"当前持仓: {current_positions} | "
                f"今日最大持仓: {self.max_positions_today} | "
                f"单股最大浮盈: {self.single_stock_max_profit:,.2f} | "
                f"单股最大浮亏: {self.single_stock_max_loss:,.2f}"
            )
            self.position_stats_label.config(text=stats_text)
            
            # 保存更新后的持仓数据
            self.save_trading_data()
            
            # 更新交易汇总
            self.update_trade_summary()
            
        except Exception as e:
            self.add_record(f"更新持仓列表失败: {str(e)}")
            print(f"更新持仓列表失败: {str(e)}")  # 添加控制台输出以便调试
    
    def display_position(self, code, volume, current_price, buy_info, virtual=False):
        """显示持仓信息"""
        if not buy_info:
            return

        buy_price = buy_info.get('buy_price')  # 使用原始买入价格
        if buy_price is None:
            self.add_record(f"警告: {code} 的买入价格为空，跳过持仓显示")
            return
        buy_price = float(buy_price)
        order_price = buy_info.get('order_price', buy_price)  # 获取委托价格，如果没有则使用买入价格
        buy_time = buy_info.get('buy_time', '')
        
        # 检查是否为CCI信号记录（仅信号，未实际买入）
        is_signal_only = buy_info.get('is_signal_only', False) and volume == 0

        if is_signal_only:
            # CCI信号记录显示
            position_text = (
                f"[CCI信号]{code} "
                f"信号价格:{buy_price:.3f} "
                f"现价:{current_price:.3f} "
                f"等待EXP3突破买入 "
                f"信号时间:{buy_time}"
            )
            color = 'blue'  # CCI信号记录用蓝色显示
        else:
            # 实际持仓计算盈亏
            profit = (current_price - buy_price) * volume

            # 安全计算盈亏百分比，避免除零错误
            if buy_price > 0:
                profit_percent = (current_price - buy_price) / buy_price * 100
            else:
                profit_percent = 0.0

            # 计算手续费（使用原始价格）
            buy_fee = buy_price * volume * 0.0003
            sell_fee = current_price * volume * 0.0003
            total_fee = buy_fee + sell_fee

            # 计算总盈亏（含手续费）
            total_profit = profit - total_fee

            # 安全计算总盈亏百分比，避免除零错误
            cost_base = buy_price * volume
            if cost_base > 0:
                total_profit_percent = total_profit / cost_base * 100
            else:
                total_profit_percent = 0.0

            # 显示持仓信息，包含数量、原始成本和委托价格
            is_double_buy = buy_info.get('is_double_buy', False)
            position_text = (
                f"{'[双倍]' if is_double_buy else ''}{'[虚拟]' if virtual else ''}{code} "
                f"数量:{volume}股 "
                f"成本:{buy_price:.3f}(委托:{order_price:.3f}) "
                f"现价:{current_price:.3f} "
                f"盈亏:{total_profit:.2f}({total_profit_percent:.2f}%) "
                f"买入:{buy_time}"
            )

            # 根据盈亏情况设置颜色
            if total_profit > 0:
                color = 'red'
            elif total_profit < 0:
                color = 'green'
            else:
                color = 'black'

        
        # 将持仓信息添加到列表框中
        self.position_listbox.insert(tk.END, position_text)
        last_index = self.position_listbox.size() - 1
        self.position_listbox.itemconfig(last_index, {'fg': color})

    def display_tech_sell_waiting_rebuy(self, code, temp_sell_info):
        """显示技术指标卖出等待重新买回的股票信息"""
        try:
            original_position = temp_sell_info['original_position']
            temp_sell_price = temp_sell_info['temp_sell_price']
            temp_sell_time = temp_sell_info['temp_sell_time']

            # 获取当前价格
            current_price = self.get_current_price(code)
            if not current_price:
                current_price = temp_sell_price  # 如果获取不到当前价格，使用暂时卖出时的价格

            buy_price = original_position['buy_price']
            quantity = original_position.get('quantity', 0)

            # 计算如果重新买回的盈亏情况
            if buy_price > 0:
                potential_profit_rate = (current_price - buy_price) / buy_price * 100
            else:
                potential_profit_rate = 0.0

            potential_profit = (current_price - buy_price) * quantity

            # 构建显示文本
            position_text = (
                f"[暂时卖出]{code} "
                f"原成本:{buy_price:.3f} "
                f"卖出价:{temp_sell_price:.3f} "
                f"现价:{current_price:.3f} "
                f"数量:{quantity}股 "
                f"潜在盈亏:{potential_profit:.2f}({potential_profit_rate:.2f}%) "
                f"卖出时间:{temp_sell_time} "
                f"等待EXP3突破重新买回"
            )

            # 根据潜在盈亏情况设置颜色
            if potential_profit > 0:
                color = 'orange'  # 橙色表示暂时卖出且潜在盈利
            elif potential_profit < 0:
                color = 'purple'  # 紫色表示暂时卖出且潜在亏损
            else:
                color = 'gray'    # 灰色表示暂时卖出且盈亏平衡

            # 添加到持仓列表
            self.position_listbox.insert(tk.END, position_text)
            self.position_listbox.itemconfig(tk.END, {'fg': color})

        except Exception as e:
            self.add_record(f"显示暂时卖出等待重新买回信息失败 {code}: {str(e)}")
            print(f"显示暂时卖出等待重新买回信息失败 {code}: {str(e)}")

    def get_current_price(self, code):
        """获取股票当前价格"""
        try:
            # 获取当前时间
            current_time = datetime.now().time()

            # 判断是否在交易时间内（15:00收盘，15:00以后为收盘后）
            is_trading_time = (
                (datetime.strptime('09:30:00', '%H:%M:%S').time() <= current_time < datetime.strptime('11:30:00', '%H:%M:%S').time()) or
                (datetime.strptime('13:00:00', '%H:%M:%S').time() <= current_time < datetime.strptime('15:00:00', '%H:%M:%S').time())
            )

            # 如果在交易时间内，优先使用实时行情
            if is_trading_time:
                try:
                    tick = xtdata.get_full_tick([code])
                    if tick and code in tick and tick[code]["lastPrice"] > 0:
                        return round(tick[code]["lastPrice"], 3)
                except Exception as e:
                    print(f"获取 {code} 实时行情失败: {str(e)}，尝试使用K线数据")

            # 非交易时间或实时行情获取失败时，使用K线数据
            today = datetime.now().strftime('%Y%m%d')

            # 首先尝试获取5分钟K线数据（与买卖点判断保持一致的方式）
            try:
                # 显示下载进度
                print(f"📥 正在下载 {code} 的5分钟K线数据...")

                # 先下载数据，与买卖点判断时保持一致
                self.downloader.my_download([code], '5m', today, today)
                time.sleep(0.1)  # 短暂等待数据就绪

                print(f"📊 正在获取 {code} 的价格数据...")
                minute_data = xtdata.get_market_data(
                    field_list=['time', 'close'],
                    stock_list=[code],
                    period='5m',
                    start_time=today,
                    end_time=today,
                    count=-1
                )

                if minute_data and code in minute_data['time'].index and len(minute_data['time'].loc[code]) > 0:
                    print(f"✅ {code} 数据获取成功，共{len(minute_data['time'].loc[code])}根K线")

                    # 创建DataFrame
                    df = pd.DataFrame({
                        'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai'),
                        'close': minute_data['close'].loc[code]
                    })

                    if not df.empty:
                        # 判断当前时间段
                        current_hour = datetime.now().hour
                        current_minute = datetime.now().minute

                        # 如果是真正的收盘后（15:00之后），查找15:00的收盘价
                        if current_hour > 15 or (current_hour == 15 and current_minute >= 0):
                            print(f"🔍 {code} 收盘后，查找15:00收盘价...")
                            closing_data = df[df['time'].dt.strftime('%H:%M:%S') == '15:00:00']
                            if not closing_data.empty:
                                price = round(closing_data['close'].iloc[-1], 3)
                                print(f"💰 {code} 15:00收盘价: {price}")
                                return price
                            else:
                                print(f"⚠️ {code} 未找到15:00收盘价，使用最后一根K线")
                        # 如果是中午休市时间（11:30-13:00），查找11:30的价格
                        elif not is_trading_time and (11 <= current_hour < 13):
                            print(f"🔍 {code} 中午休市，查找11:30价格...")
                            noon_data = df[df['time'].dt.strftime('%H:%M:%S') == '11:30:00']
                            if not noon_data.empty:
                                price = round(noon_data['close'].iloc[-1], 3)
                                print(f"💰 {code} 11:30价格: {price}")
                                return price
                            else:
                                print(f"⚠️ {code} 未找到11:30价格，使用最后一根K线")

                        # 使用最后一根K线的收盘价
                        last_time = df['time'].iloc[-1].strftime('%H:%M:%S')
                        price = round(df['close'].iloc[-1], 3)
                        print(f"💰 {code} 最后K线({last_time})价格: {price}")
                        return price
                    else:
                        print(f"❌ {code} DataFrame为空")
                else:
                    print(f"❌ {code} 数据获取失败或无数据")
            except Exception as e:
                print(f"❌ {code} 5分钟K线数据获取异常: {str(e)}")

            print(f"❌ 无法获取 {code} 的价格数据")
            return None

        except Exception as e:
            print(f"获取 {code} 当前价格时出错: {str(e)}")
            return None


    def manual_refresh_positions(self):
        """手动刷新持仓价格"""
        try:
            self.add_record("🔄 手动刷新持仓价格...")

            # 更新持仓列表
            self.update_position_list()
            self.update_trade_summary()

            self.add_record("✅ 手动刷新持仓价格完成")

        except Exception as e:
            self.add_record(f"手动刷新持仓价格失败: {str(e)}")
    
    def update_trade_summary(self):
        """更新交易汇总信息"""
        try:
            # 计算当前持仓数和浮动盈亏
            current_positions = 0
            total_float_profit = 0.0
            total_cost = 0.0
            
            # 判断是否在交易时间
            current_time = datetime.now().time()
            is_trading_time = (
                (datetime.strptime('09:30:00', '%H:%M:%S').time() <= current_time < datetime.strptime('11:30:00', '%H:%M:%S').time()) or
                (datetime.strptime('13:00:00', '%H:%M:%S').time() <= current_time < datetime.strptime('15:00:00', '%H:%M:%S').time())
            )

            for code in list(self.position_records.keys()):
                position_info = self.position_records[code]

                if is_trading_time:
                    # 交易时间内，每次都获取最新价格
                    current_price = self.get_current_price(code)
                else:
                    # 收盘后，优先使用缓存的价格，避免重复下载
                    current_price = position_info.get('current_price')
                    if not current_price:
                        # 如果没有缓存价格，才重新获取
                        current_price = self.get_current_price(code)
                        if current_price:
                            # 缓存获取到的价格
                            self.position_records[code]['current_price'] = current_price

                if current_price is None:
                    continue
                
                position_info = self.position_records[code]
                volume = position_info.get('quantity', 0)
                buy_price = position_info.get('buy_price')
                if buy_price is None:
                    self.add_record(f"警告: {code} 的买入价格为空，跳过交易汇总计算")
                    continue
                buy_price = float(buy_price)
                
                # 计算成本和浮动盈亏（与持仓显示保持一致，扣除手续费）
                cost = buy_price * volume
                gross_profit = (current_price - buy_price) * volume

                # 计算手续费
                buy_fee = buy_price * volume * 0.0003
                sell_fee = current_price * volume * 0.0003
                total_fee = buy_fee + sell_fee

                # 净浮动盈亏（扣除手续费）
                float_profit = gross_profit - total_fee

                total_cost += cost
                total_float_profit += float_profit
                current_positions += 1
            
            # 更新当日最大浮盈和最大浮亏
            if total_float_profit > self.daily_max_float_profit:
                self.daily_max_float_profit = total_float_profit
            if total_float_profit < self.daily_max_float_loss:
                self.daily_max_float_loss = total_float_profit
            
            # 更新持仓数量统计
            if current_positions > self.max_positions_today:
                self.max_positions_today = current_positions
            
            # 计算交易统计
            total_trades = len(self.trade_records)
            profit_trades = sum(1 for record in self.trade_records if record['profit'] > 0)
            loss_trades = sum(1 for record in self.trade_records if record['profit'] <= 0)

            # 计算技术指标卖出等待重新买回的股票数量
            temp_sell_count = len(self.tech_sell_waiting_rebuy)

            # 构建显示文本
            summary_text = (
                f"总交易: {total_trades}次 盈利: {profit_trades}次 亏损: {loss_trades}次\n"
                f"当前浮盈: {total_float_profit:,.2f}元\n"
                f"当日最大浮盈: {self.daily_max_float_profit:,.2f}元\n"
                f"当日最大浮亏: {self.daily_max_float_loss:,.2f}元\n"
                f"持仓成本: {total_cost:,.2f}元\n"
                f"当日总盈亏: {self.daily_total_profit:,.2f}元\n"
                f"暂时卖出等待重新买回: {temp_sell_count}只"
            )
            
            # 更新显示
            self.trade_summary_label.config(text=summary_text)
            
        except Exception as e:
            self.add_record(f"更新交易汇总失败: {str(e)}")
            print(f"更新交易汇总失败: {str(e)}")  # 添加控制台输出以便调试
    
    def import_trading_data(self):
        """从文件导入交易数据"""
        try:
            # 打开文件选择对话框
            position_file = filedialog.askopenfilename(
                title="选择持仓记录文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 加载持仓记录
            with open(position_file, 'r', encoding='utf-8') as f:
                self.position_records = json.load(f)
            
            # 更新持仓列表
            self.update_position_list()
            
            # 提示用户选择交易历史文件
            trade_file = filedialog.askopenfilename(
                title="选择交易历史文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if trade_file:
                # 加载交易历史
                with open(trade_file, 'r', encoding='utf-8') as f:
                    self.trade_records = json.load(f)
                
                # 更新交易汇总
                self.update_trade_summary()
            
            # 保存到当日文件
            self.save_trading_data()
            
            messagebox.showinfo("成功", f"已导入 {len(self.position_records)} 条持仓记录和 {len(self.trade_records)} 条交易历史")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入交易数据失败: {str(e)}")

    def export_trading_data(self):
        """导出交易数据到用户指定文件"""
        try:
            # 选择保存持仓记录的位置
            position_file = filedialog.asksaveasfilename(
                title="保存持仓记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"持仓记录_{self.today}_导出.json"
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 保存持仓记录
            with open(position_file, 'w', encoding='utf-8') as f:
                json.dump(self.position_records, f, ensure_ascii=False, indent=2)
            
            # 选择保存交易历史的位置
            trade_file = filedialog.asksaveasfilename(
                title="保存交易历史",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"交易历史_{self.today}_导出.json"
            )
            
            if trade_file:
                # 保存交易历史
                with open(trade_file, 'w', encoding='utf-8') as f:
                    json.dump(self.trade_records, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "交易数据导出成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出交易数据失败: {str(e)}")

    def monitor_orders(self):
        """监控委托状态的线程函数"""
        # 添加委托状态缓存
        order_status_cache = {}
        
        while self.monitoring:
            try:
                # 检查是否在交易时间
                current_time = datetime.now().time()
                if not (('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or 
                       ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '14:57:00')):
                    time.sleep(60)
                    continue
                
                # 如果没有未完成的买卖指令，直接跳过
                if not self.trade_instructions:
                    time.sleep(10)
                    continue
                
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 获取需要查询的股票列表
                monitor_codes = list(self.trade_instructions.keys())
                
                # 查询当日委托
                request = {
                    'type': 'query_orders',
                    'params': {
                        'start_date': today,
                        'end_date': today,
                        'stock_codes': monitor_codes
                    }
                }
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                    time.sleep(10)
                    continue
                
                # 检查每个委托的状态
                for order in response['data']:
                    code = order['stock_code']
                    order_id = order['order_id']
                    status = order['order_status']
                    volume = order['traded_volume']
                    
                    # 检查委托状态是否发生变化
                    cache_key = f"{code}_{order_id}"
                    if cache_key in order_status_cache:
                        cached_status = order_status_cache[cache_key]
                        if cached_status['status'] == status and cached_status['volume'] == volume:
                            continue  # 状态未变化，跳过输出
                    
                    # 更新缓存
                    order_status_cache[cache_key] = {
                        'status': status,
                        'volume': volume
                    }
                    
                    # 输出委托详细信息
                    self.add_record(f"检查委托: {code} 委托号: {order_id} 状态: {status} 成交量: {volume}")
                    
                    # 检查委托号是否匹配
                    if code in self.trade_instructions:
                        instruction = self.trade_instructions[code]
                        if order_id != instruction['order_id']:
                            self.add_record(f"跳过不匹配的委托: {code} 期望: {instruction['order_id']} 实际: {order_id}")
                            continue
                        
                        # 检查委托状态
                        if status in [56, 57]:  # 已成、废单
                            self.add_record(f"委托已完成: {code} 状态: {status}")
                            
                            # 如果已成交，立即更新成交价格
                            if status == 56 and volume > 0:
                                self.process_trade_reports(code, order_id)
                                
                            del self.trade_instructions[code]  # 删除已完成的指令
                            if cache_key in order_status_cache:
                                del order_status_cache[cache_key]  # 删除缓存
                            continue
                        
                        # 检查成交量
                        if volume == 0:
                            # 获取委托时长(秒)
                            order_time = datetime.fromtimestamp(order['order_time']/1000)
                            elapsed_seconds = (datetime.now() - order_time).total_seconds()
                            
                            # 如果委托超过10秒未成交，执行撤单重试
                            if elapsed_seconds >= 10:
                                self.add_record(f"开始重试委托: {code} 委托号: {order_id} 已等待: {elapsed_seconds}秒")
                                self.retry_failed_order(order)
                        elif volume > 0 and status == 55:  # 部分成交
                            # 有部分成交，立即处理成交回报
                            self.process_trade_reports(code, order_id)

                # 定时处理所有持仓的成交回报，确保价格更新
                self.process_all_trade_reports()
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                self.add_record(f"监控委托出错: {str(e)}")
                time.sleep(10)

    def process_trade_reports(self, code=None, order_id=None):
        """处理成交回报"""
        try:
            # 查询成交回报
            request = {'type': 'query_trade_reports'}
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询成交回报失败: {response.get('message', '未知错误')}")
                return
            
            # 处理成交回报
            for trade in response['data']:
                trade_code = trade['stock_code']
                trade_direction = trade['direction']
                trade_volume = trade['volume']
                trade_price = trade['price']
                trade_order_id = trade['order_id']
                trade_amount = trade_price * trade_volume
                
                # 如果指定了code和order_id，只处理匹配的成交回报
                if code and order_id:
                    if trade_code != code or trade_order_id != order_id:
                        continue
                
                # 记录成交信息
                self.add_record(f"成交回报: {trade_code} 委托号:{trade_order_id} 方向:{'买入' if trade_direction == STOCK_BUY else '卖出'} "
                              f"数量:{trade_volume} 价格:{trade_price:.3f} 金额:{trade_amount:.2f}")
                
                # 处理买入成交
                if trade_direction == STOCK_BUY:
                    # 更新持仓记录的买入价格
                    if trade_code in self.position_records:
                        position_info = self.position_records[trade_code]
                        original_price = position_info['buy_price']
                        position_info['buy_price'] = trade_price
                        self.add_record(f"已更新 {trade_code} 的买入价格: {original_price:.3f} -> {position_info['buy_price']:.3f}")
                
                # 处理卖出成交
                elif trade_direction == STOCK_SELL:
                    # 计算卖出盈亏
                    if trade_code in self.position_records:
                        position_info = self.position_records[trade_code]
                        buy_price = position_info['buy_price']
                        buy_volume = position_info['quantity']
                        
                        # 计算盈亏
                        profit = (trade_price - buy_price) * trade_volume
                        
                        # 更新当日总盈亏
                        self.daily_total_profit += profit
                        
                        # 如果全部卖出，清理持仓记录
                        if trade_volume >= buy_volume:
                            if trade_code in self.position_records:
                                del self.position_records[trade_code]
                            if trade_code in self.waiting_for_profit_sell:
                                del self.waiting_for_profit_sell[trade_code]
                            if trade_code in self.trade_instructions:
                                del self.trade_instructions[trade_code]
                        else:
                            # 部分卖出，更新剩余持仓
                            position_info['quantity'] = buy_volume - trade_volume
                
                # 更新界面显示
                self.update_position_list()
                self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"处理成交回报失败: {str(e)}")

    def process_all_trade_reports(self):
        """处理所有持仓的成交回报，确保价格更新"""
        try:
            # 只有在启用实际交易时才进行处理
            if not self.trading_enabled or not self.position_records:
                return
                
            # 处理所有持仓
            self.process_trade_reports()
            
        except Exception as e:
            self.add_record(f"处理所有成交回报时出错: {str(e)}")

    def retry_failed_order(self, failed_order):
        """重试失效的委托"""
        try:
            code = failed_order['stock_code']
            
            # 检查重试次数
            if not hasattr(self, 'retry_counts'):
                self.retry_counts = {}
            
            # 如果已经取消过交易，直接返回
            if code not in self.retry_counts:
                self.retry_counts[code] = 1
            else:
                # 如果已经达到3次，说明已经取消过交易，直接返回
                if self.retry_counts[code] >= 3:
                    self.add_record(f"{code} 委托连续失败3次，取消交易")
                    if code in self.trade_instructions:
                        del self.trade_instructions[code]  # 删除指令
                    if code in self.position_records:
                        del self.position_records[code]
                    return
                self.retry_counts[code] += 1
            
            # 重新获取最新价格
            current_price = self.get_current_price(code)
            if current_price:
                time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                if code in self.trade_instructions:
                    instruction = self.trade_instructions[code]
                    if instruction['type'] == 'sell':
                        # 卖出重试，降价重试
                        new_price = current_price - 0.40
                        self.execute_sell(code, new_price, time_str)
                    else:
                        # 买入重试，加价重试
                        new_price = current_price + 0.40
                        self.execute_buy(code, new_price, time_str)
                        
        except Exception as e:
            self.add_record(f"重试委托失败 {code}: {str(e)}")

    def test_order_functions(self):
        """测试下单查询功能"""
        try:
            self.add_record("开始测试下单查询功能...")
            test_code = "127055.SZ"  # 使用大写的SZ
            test_records = []  # 记录测试过程中产生的委托号
            
            # 先测试连接
            test_request = {'type': 'query_asset'}
            test_response = self.send_request(test_request)
            if test_response['status'] != 'success':
                self.add_record("无法连接到委托服务器，请确保委托查询撤单程序已启动")
                return
            
            try:
                # 1. 测试买入委托
                price = 163.0
                time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                self.add_record(f"测试买入委托 {test_code}")
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': test_code,
                        'direction': STOCK_BUY,
                        'volume': 10,  # 测试用小数量
                        'price_type': FIX_PRICE,
                        'price': price
                    }
                }
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    order_id = response['data']['order_id']
                    test_records.append(order_id)
                    self.add_record(f"测试买入委托已提交，委托号: {order_id}")
                
                # 等待3秒以确保买入委托请求完成
                self.add_record("等待3秒...")
                time.sleep(3)
                
                # 2. 查询委托状态
                request = {'type': 'query_orders'}
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    self.add_record("委托查询成功:")
                    for order in response['data']:
                        status_map = {
                            48: "未报", 49: "待报", 50: "已报", 51: "已报待撤",
                            52: "部成待撤", 53: "部撤", 54: "已撤", 55: "部成",
                            56: "已成", 57: "废单", 255: "未知"
                        }
                        status = status_map.get(order['order_status'], "未知")
                        self.add_record(f"委托号: {order['order_id']}, 状态: {status}")
                
                # 3. 测试查询资产
                asset_request = {'type': 'query_asset'}
                asset_response = self.send_request(asset_request)
                
                if asset_response['status'] == 'success':
                    self.add_record("资产查询成功:")
                    self.add_record(f"可用资金: {asset_response['data']['cash']:,.2f}")
                
                # 4. 测试查询持仓
                position_request = {'type': 'query_positions'}
                position_response = self.send_request(position_request)
                
                if position_response['status'] == 'success':
                    self.add_record("持仓查询成功:")
                    for pos in position_response['data']:
                        self.add_record(f"股票: {pos['stock_code']}, 数量: {pos['volume']}")
                
            finally:
                # 清理测试产生的委托
                self.add_record("开始清理测试委托...")
                
                # 撤销所有测试委托
                for order_id in test_records:
                    cancel_request = {
                        'type': 'cancel_order',
                        'params': {
                            'order_id': order_id
                        }
                    }
                    cancel_response = self.send_request(cancel_request)
                    if cancel_response['status'] == 'success':
                        self.add_record(f"已撤销测试委托 {order_id}")
                
                # 从持仓记录中移除测试股票
                if test_code in self.position_records:
                    del self.position_records[test_code]
                    self.update_position_list()
                    self.save_trading_data()
                
                self.add_record("测试清理完成")
            
            self.add_record("测试功能执行完成")
            
        except Exception as e:
            self.add_record(f"测试过程出错: {str(e)}")
            # 确保即使出错也尝试清理
            if test_code in self.position_records:
                del self.position_records[test_code]
                self.update_position_list()
                self.save_trading_data()

    def run(self):
        """运行程序"""
        try:
            self.root.mainloop()
        finally:
            # 程序关闭时保存交易数据
            self.save_trading_data()

    def cancel_order(self, code, order_id):
        """撤单操作"""
        try:
            request = {
                'type': 'cancel_order',
                'params': {
                    'order_id': order_id
                }
            }
            
            response = self.send_request(request)
            if response['status'] == 'success':
                self.add_record(f"撤单请求已提交 - {code} 委托号: {order_id}")
                self.add_record(f"撤单序号: {response['data'].get('cancel_seq')}")
                self.add_record(f"柜台合同编号: {response['data'].get('order_sysid')}")
                return True
            else:
                self.add_record(f"撤单失败 - {code} 委托号: {order_id}: {response.get('message', '未知错误')}")
                return False
            
        except Exception as e:
            self.add_record(f"撤单异常 - {code} 委托号: {order_id}: {str(e)}")
            return False

    def show_position_menu(self, event):
        """显示持仓右键菜单"""
        try:
            # 获取点击位置对应的项
            index = self.position_listbox.nearest(event.y)
            if index >= 0:
                # 选中该项
                self.position_listbox.selection_clear(0, tk.END)
                self.position_listbox.selection_set(index)
                # 显示菜单
                self.position_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.add_record(f"显示菜单失败: {str(e)}")

    def view_selected_position(self):
        """查看选中持仓的分时图"""
        selection = self.position_listbox.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        position_text = self.position_listbox.get(selection[0])
        code = self.extract_code_from_position_text(position_text)

        if not code:
            messagebox.showerror("错误", "无法从持仓信息中提取股票代码")
            return

        threading.Thread(target=lambda: self.check_and_show_chart(code)).start()

    def clear_selected_position(self):
        """清除选中的持仓记录"""
        selection = self.position_listbox.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        position_text = self.position_listbox.get(selection[0])

        # 从持仓文本中提取股票代码，处理可能的标记前缀
        code = self.extract_code_from_position_text(position_text)

        if not code:
            messagebox.showerror("错误", "无法从持仓信息中提取股票代码")
            return

        # 确认是否清除
        if messagebox.askyesno("确认", f"确定要清除 {code} 的持仓记录吗？\n注意：这只是清除记录，不会执行实际的卖出操作。"):
            if code in self.position_records:
                del self.position_records[code]
                self.add_record(f"已清除 {code} 的持仓记录")
                # 更新界面和保存数据
                self.update_position_list()
                self.update_trade_summary()
                self.save_trading_data()
            else:
                messagebox.showwarning("警告", f"未找到 {code} 的持仓记录")
                self.add_record(f"警告：未找到 {code} 的持仓记录")

    def extract_code_from_position_text(self, position_text):
        """从持仓文本中提取股票代码"""
        try:
            # 持仓文本格式示例：
            # "[CCI信号]123456.SZ 信号价格:100.000 现价:101.000 等待EXP3突破买入 信号时间:10:30:00"
            # "[双倍][虚拟]123456.SZ 数量:100股 成本:100.000(委托:100.200) 现价:101.000 盈亏:100.00(1.00%) 买入:10:30:00"
            # "123456.SZ 数量:100股 成本:100.000(委托:100.200) 现价:101.000 盈亏:100.00(1.00%) 买入:10:30:00"

            import re

            # 使用正则表达式匹配股票代码格式：6位数字.SZ或.SH
            pattern = r'(\d{6}\.[A-Z]{2})'
            match = re.search(pattern, position_text)

            if match:
                return match.group(1)
            else:
                # 如果正则匹配失败，尝试其他方法
                # 移除所有方括号标记，然后取第一个词
                cleaned_text = re.sub(r'\[[^\]]*\]', '', position_text).strip()
                parts = cleaned_text.split()
                if parts:
                    # 检查第一个部分是否像股票代码
                    first_part = parts[0]
                    if '.' in first_part and len(first_part.split('.')[0]) == 6:
                        return first_part

                return None

        except Exception as e:
            self.add_record(f"提取股票代码失败: {str(e)}")
            return None

    def show_stock_menu(self, event):
        """显示股票列表右键菜单"""
        try:
            # 确定是哪个列表框触发了事件
            widget = event.widget

            # 获取点击位置对应的项
            index = widget.nearest(event.y)
            if index >= 0 and index < widget.size():
                # 选中该项
                widget.selection_clear(0, tk.END)
                widget.selection_set(index)

                # 记录当前操作的列表框，用于后续操作
                self.current_stock_listbox = widget

                # 显示菜单
                self.stock_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.add_record(f"显示股票菜单失败: {str(e)}")

    def view_selected_stock(self):
        """查看选中股票的分时图"""
        try:
            if not hasattr(self, 'current_stock_listbox'):
                messagebox.showinfo("提示", "请先选择一个股票")
                return

            selection = self.current_stock_listbox.curselection()
            if not selection:
                messagebox.showinfo("提示", "请先选择一个股票")
                return

            code = self.current_stock_listbox.get(selection[0])
            threading.Thread(target=lambda: self.check_and_show_chart(code)).start()
        except Exception as e:
            self.add_record(f"查看股票分时图失败: {str(e)}")

    def remove_selected_stock(self):
        """从股票池中移除选中的股票"""
        try:
            if not hasattr(self, 'current_stock_listbox'):
                messagebox.showinfo("提示", "请先选择一个股票")
                return

            selection = self.current_stock_listbox.curselection()
            if not selection:
                messagebox.showinfo("提示", "请先选择一个股票")
                return

            code = self.current_stock_listbox.get(selection[0])

            # 确认是否移除
            if messagebox.askyesno("确认移除", f"确定要从股票池中移除 {code} 吗？"):
                # 从可转债列表中移除
                if code in self.bond_codes:
                    self.bond_codes.remove(code)
                    self.add_record(f"已从可转债池中移除 {code}")

                # 更新合并的股票代码列表
                self.stock_codes = self.bond_codes

                # 刷新界面显示
                self.refresh_stock_lists()

                # 更新窗口标题
                total_codes = len(self.bond_codes)
                self.root.title(f"可转债做T交易 - {total_codes}只可转债")

                # 保存股票池到文件
                self.save_stock_pool_to_file()

        except Exception as e:
            self.add_record(f"移除股票失败: {str(e)}")

    def refresh_stock_lists(self):
        """刷新股票列表显示"""
        try:
            # 清空并重新填充可转债列表框
            self.bond_listbox.delete(0, tk.END)
            for code in self.bond_codes:
                self.bond_listbox.insert(tk.END, code)

        except Exception as e:
            self.add_record(f"刷新股票列表失败: {str(e)}")

    def save_stock_pool_to_file(self):
        """保存股票池到文件"""
        try:
            stock_pool_data = {
                'bond_codes': getattr(self, 'bond_codes', []),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.stock_pool_file, 'w', encoding='utf-8') as f:
                json.dump(stock_pool_data, f, ensure_ascii=False, indent=2)

            print(f"股票池已保存到 {self.stock_pool_file}")

        except Exception as e:
            print(f"保存股票池失败: {str(e)}")
            self.add_record(f"保存股票池失败: {str(e)}")

    def load_profit_blacklist(self):
        """加载获利股票黑名单"""
        try:
            if os.path.exists(self.profit_blacklist_file):
                with open(self.profit_blacklist_file, 'r', encoding='utf-8') as f:
                    blacklist_data = json.load(f)
                    blacklist = set(blacklist_data.get('blacklist', []))
                    print(f"已加载获利股票黑名单: {len(blacklist)} 只股票")
                    if hasattr(self, 'add_record'):
                        self.add_record(f"已加载获利股票黑名单: {len(blacklist)} 只股票")
                    return blacklist
            else:
                print("获利股票黑名单文件不存在，初始化空黑名单")
                return set()
        except Exception as e:
            print(f"加载获利股票黑名单失败: {str(e)}")
            if hasattr(self, 'add_record'):
                self.add_record(f"加载获利股票黑名单失败: {str(e)}")
            return set()

    def save_profit_blacklist(self):
        """保存获利股票黑名单"""
        try:
            blacklist_data = {
                'blacklist': list(self.profit_blacklist),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(self.profit_blacklist)
            }

            with open(self.profit_blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(blacklist_data, f, ensure_ascii=False, indent=2)

            print(f"获利股票黑名单已保存: {len(self.profit_blacklist)} 只股票")

            # 更新界面显示
            self.update_blacklist_status()

        except Exception as e:
            print(f"保存获利股票黑名单失败: {str(e)}")
            if hasattr(self, 'add_record'):
                self.add_record(f"保存获利股票黑名单失败: {str(e)}")

    def update_blacklist_status(self):
        """更新黑名单状态标签"""
        try:
            if hasattr(self, 'blacklist_status_label'):
                blacklist_count = len(self.profit_blacklist)
                self.blacklist_status_label.config(text=f"获利黑名单: {blacklist_count} 只股票")
        except Exception as e:
            print(f"更新黑名单状态标签失败: {str(e)}")

    def add_to_profit_blacklist(self, code):
        """将股票添加到获利黑名单"""
        if code not in self.profit_blacklist:
            self.profit_blacklist.add(code)
            self.save_profit_blacklist()
            self.add_record(f"🚫 {code} 已添加到获利股票黑名单，今后不再买入")
            return True
        return False

    def remove_from_profit_blacklist(self, code):
        """从获利黑名单中移除股票"""
        if code in self.profit_blacklist:
            self.profit_blacklist.remove(code)
            self.save_profit_blacklist()
            self.add_record(f"✅ {code} 已从获利股票黑名单中移除")
            return True
        return False

    def clean_profit_blacklist(self):
        """清理获利股票黑名单，移除不在当前股票池中的股票"""
        try:
            if not hasattr(self, 'bond_codes') or not self.bond_codes:
                self.add_record("⚠️ 股票池为空，跳过黑名单清理")
                return

            current_stock_set = set(self.bond_codes)
            removed_stocks = []

            # 找出不在股票池中的黑名单股票
            for code in list(self.profit_blacklist):
                if code not in current_stock_set:
                    self.profit_blacklist.remove(code)
                    removed_stocks.append(code)

            if removed_stocks:
                self.save_profit_blacklist()
                self.add_record(f"🧹 已从获利黑名单中清理 {len(removed_stocks)} 只不在股票池中的股票: {', '.join(removed_stocks)}")
            else:
                self.add_record("✅ 获利黑名单清理完成，无需清理的股票")

        except Exception as e:
            self.add_record(f"清理获利黑名单失败: {str(e)}")

    def load_stock_pool_from_file(self):
        """从文件加载股票池"""
        try:
            if os.path.exists(self.stock_pool_file):
                with open(self.stock_pool_file, 'r', encoding='utf-8') as f:
                    stock_pool_data = json.load(f)

                self.bond_codes = stock_pool_data.get('bond_codes', [])
                self.etf_codes = []  # 保持兼容性，但不再使用
                self.stock_codes = self.bond_codes

                # 更新界面显示
                self.refresh_stock_lists()

                # 更新窗口标题
                total_codes = len(self.bond_codes)
                self.root.title(f"可转债做T交易 - {total_codes}只可转债")

                last_updated = stock_pool_data.get('last_updated', '未知')
                self.add_record(f"已从文件加载股票池: {total_codes}只可转债 (更新时间: {last_updated})")
                print(f"成功从 {self.stock_pool_file} 加载股票池")

            else:
                # 文件不存在，初始化空的股票池
                self.bond_codes = []
                self.etf_codes = []  # 保持兼容性
                self.stock_codes = []

                # 创建默认的股票池文件
                self.save_stock_pool_to_file()

                self.add_record("股票池文件不存在，已创建空的可转债池")
                print(f"股票池文件 {self.stock_pool_file} 不存在，已创建空的可转债池")

        except Exception as e:
            print(f"加载股票池失败: {str(e)}")
            self.add_record(f"加载股票池失败: {str(e)}")

    def read_volume_bond_codes(self):
        """从放量可转债.txt文件中读取代码"""
        try:
            if not os.path.exists(self.volume_bond_file):
                self.add_record(f"文件 {self.volume_bond_file} 不存在")
                return []

            codes = []
            with open(self.volume_bond_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                # 跳过注释行和空行
                if line and not line.startswith('#'):
                    # 检查是否是有效的可转债代码格式
                    if (line.endswith('.SH') or line.endswith('.SZ')) and len(line) == 9:
                        codes.append(line)

            self.add_record(f"从 {self.volume_bond_file} 读取到 {len(codes)} 个可转债代码")
            return codes

        except Exception as e:
            self.add_record(f"读取 {self.volume_bond_file} 失败: {str(e)}")
            return []

    def auto_refresh_stock_pool(self):
        """自动刷新股票池（从放量可转债.txt补充代码）"""
        try:
            # 读取新的可转债代码
            new_codes = self.read_volume_bond_codes()

            if not new_codes:
                self.add_record("未读取到新的可转债代码")
                return

            # 获取当前股票池
            current_codes = getattr(self, 'bond_codes', [])
            current_set = set(current_codes)

            # 补充新代码（去重）
            added_count = 0
            for code in new_codes:
                if code not in current_set:
                    current_codes.append(code)
                    current_set.add(code)
                    added_count += 1

            # 更新股票池
            self.bond_codes = current_codes
            self.stock_codes = self.bond_codes

            # 刷新界面显示
            self.refresh_stock_lists()

            # 更新窗口标题
            total_codes = len(self.bond_codes)
            self.root.title(f"可转债做T交易 - {total_codes}只可转债")

            # 保存股票池到文件
            self.save_stock_pool_to_file()

            # 记录结果
            if added_count > 0:
                self.add_record(f"✅ 自动刷新股票池完成：新增 {added_count} 只可转债，当前总计 {total_codes} 只")
            else:
                self.add_record(f"✅ 自动刷新股票池完成：无新增代码，当前总计 {total_codes} 只")

        except Exception as e:
            self.add_record(f"自动刷新股票池失败: {str(e)}")

    def manual_refresh_stock_pool(self):
        """手动刷新股票池（用于测试）"""
        try:
            self.add_record("🔄 手动触发股票池刷新...")
            self.auto_refresh_stock_pool()
        except Exception as e:
            self.add_record(f"手动刷新股票池失败: {str(e)}")

            # 加载失败时初始化空的股票池
            self.bond_codes = []
            self.etf_codes = []  # 保持兼容性
            self.stock_codes = []

    def clear_pending_orders(self):
        """清理所有未完成的委托"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取所有委托，添加日期参数
            request = {
                'type': 'query_orders',
                'params': {
                    'start_date': today,
                    'end_date': today
                }
            }
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                return
            
            # 统计需要清理的委托
            pending_orders = [order for order in response['data']
                            if order['order_status'] in [48, 49, 50, 51, 52, 55]]  # 只处理未完成的委托
            
            if not pending_orders:
                self.add_record("没有需要清理的未完成委托")
                return
            
            self.add_record(f"开始清理 {len(pending_orders)} 个未完成委托...")
            
            # 撤销未完成的委托
            for order in pending_orders:
                cancel_request = {
                    'type': 'cancel_order',
                    'params': {
                        'order_id': order['order_id']
                    }
                }
                cancel_response = self.send_request(cancel_request)
                
                if cancel_response['status'] == 'success':
                    self.add_record(f"已撤销委托 - {order['stock_code']} 委托号:{order['order_id']}")
            
            # 清空重试计数器
            if hasattr(self, 'retry_counts'):
                self.retry_counts.clear()
            
            # 清空当日的持仓记录
            self.position_records.clear()
            self.update_position_list()
            self.save_trading_data()
            self.add_record("已清空当日持仓记录")
            
            self.add_record("清理完成")
            
        except Exception as e:
            self.add_record(f"清理委托时出错: {str(e)}")

    def check_connection(self):
        """检查与服务器的连接状态"""
        try:
            test_request = {'type': 'query_asset'}
            response = self.send_request(test_request)
            
            if response['status'] == 'success':
                if not self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 已连接", foreground="green")
                        self.root.update()
                    # 再添加记录
                    self.add_record("已连接到服务器")
                self.is_connected = True
            else:
                if self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                        self.root.update()
                    # 再添加记录
                    self.add_record("与服务器断开连接")
                self.is_connected = False
                
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
        
        # 每5秒检查一次
        self.root.after(5000, self.check_connection)

    def update_realtime_hint(self, text):
        """更新实时提示标签"""
        try:
            # 更新标签文本
            self.realtime_hint_label.config(text=f"实时提示: {text}")
            
            # 根据提示类型设置颜色
            if "警告" in text:
                self.realtime_hint_label.config(foreground="red")
            elif "卖出信号" in text:  # 卖出信号改为绿色
                self.realtime_hint_label.config(foreground="green")
            elif "买入信号" in text:  # 买入信号改为橙色
                self.realtime_hint_label.config(foreground="orange")
            else:
                self.realtime_hint_label.config(foreground="black")
        except Exception as e:
            print(f"更新实时提示失败: {str(e)}")

    def calculate_indicators(self, df):
        try:
            # 计算CCI指标 (14周期)
            n = 14
            # 计算典型价格 TP = (HIGH + LOW + CLOSE) / 3
            df['tp'] = (df['high'] + df['low'] + df['close']) / 3

            # 计算TP的简单移动平均
            df['sma_tp'] = df['tp'].rolling(window=n).mean()

            # 计算平均绝对偏差 MD
            df['md'] = df['tp'].rolling(window=n).apply(lambda x: abs(x - x.mean()).mean())

            # 计算CCI = (TP - SMA_TP) / (0.015 * MD)，避免除零错误
            md_factor = 0.015 * df['md']
            df['cci'] = np.where(md_factor != 0, (df['tp'] - df['sma_tp']) / md_factor, 0)

            # 计算EXP3指标
            # EXP3 = REF(HHV(CLOSE,P),1) - M*REF(ATR,1)
            # 参数设置
            p = 20  # HHV周期
            m = 1.0  # ATR乘数
            atr_n = 14  # ATR周期

            # 计算ATR
            df['tr'] = np.maximum(
                np.maximum(df['high'] - df['low'],
                          abs(df['high'] - df['close'].shift(1))),
                abs(df['low'] - df['close'].shift(1))
            )
            df['atr'] = df['tr'].rolling(window=atr_n).mean()

            # 计算HHV(CLOSE,P)
            df['hhv_close'] = df['close'].rolling(window=p).max()

            # 计算EXP3 = REF(HHV(CLOSE,P),1) - M*REF(ATR,1)
            df['exp3'] = df['hhv_close'].shift(1) - m * df['atr'].shift(1)

            # 计算EMA50指标
            df['ema50'] = df['close'].ewm(span=50).mean()

            return df
        except Exception as e:
            print(f"[调试] 计算指标异常: {str(e)}")
            self.add_record(f"计算指标失败: {str(e)}")
            return None

    def print_cci_analysis(self, code, df, latest_price):
        """打印CCI指标分析情况"""
        try:
            if df is None or len(df) < 2:
                print(f"[调试] {code} DataFrame为空或长度不足，无法分析")
                self.write_log(f"[调试] {code} DataFrame为空或长度不足，无法分析")
                return

            # 获取当前和前一根K线数据
            current_kline_data = self.get_current_kline_data(df, datetime.now().hour, datetime.now().minute)
            prev_kline_data = self.get_prev_kline_data(df, datetime.now().hour, datetime.now().minute)

            if current_kline_data.empty or prev_kline_data.empty:
                print(f"[调试] {code} 获取K线数据失败")
                self.write_log(f"[调试] {code} 获取K线数据失败")
                return

            # 构建分析结果字符串
            analysis_result = f"\n[调试] {code} CCI信号分析:\n"
            analysis_result += f"当前时间: {datetime.now().strftime('%H:%M:%S')}\n"
            analysis_result += f"当前价格: {latest_price:.3f}\n"

            # CCI指标分析
            current_cci = current_kline_data['cci'].iloc[-1]
            prev_cci = prev_kline_data['cci'].iloc[-1]

            # 买入条件判断
            analysis_result += "\n=== 买入条件判断 ===\n"

            # 买入次数限制检查
            buy_count = 0
            can_initial_buy = True
            can_add_buy = True

            if code in self.position_records:
                position_info = self.position_records[code]
                buy_count = position_info.get('add_count', 0) + 1  # 包括首次买入
                can_initial_buy = False  # 已有持仓，不能首次买入
                can_add_buy = buy_count < 2  # 最多2次买入

            analysis_result += f"📊 买入次数限制检查:\n"
            analysis_result += f"  - 当前买入次数: {buy_count}/2\n"
            analysis_result += f"  - 可以首次买入: {'✅' if can_initial_buy else '❌'}\n"
            analysis_result += f"  - 可以加仓买入: {'✅' if can_add_buy else '❌'}\n"

            # CCI上穿-100条件（首次买入）
            cci_cross_condition = prev_cci <= -100 and current_cci > -100

            # 获取EMA50值和收盘价
            current_ema50 = current_kline_data['ema50'].iloc[-1] if 'ema50' in current_kline_data.columns else np.nan
            current_close = current_kline_data['close'].iloc[-1]
            ema50_condition = not pd.isna(current_ema50) and current_close < current_ema50 * 1.01

            analysis_result += "\n1. CCI上穿-100买入条件（首次买入）:\n"
            analysis_result += f"  - 前一K线CCI({prev_cci:.2f}) <= -100: {'✅' if prev_cci <= -100 else '❌'}\n"
            analysis_result += f"  - 当前K线CCI({current_cci:.2f}) > -100: {'✅' if current_cci > -100 else '❌'}\n"
            analysis_result += f"  - CCI上穿-100条件: {'✅ 满足' if cci_cross_condition else '❌ 不满足'}\n"

            # 添加EMA50条件分析
            if not pd.isna(current_ema50):
                analysis_result += f"  - 收盘价({current_close:.3f}) < EMA50*1.01({current_ema50*1.01:.3f}): {'✅' if ema50_condition else '❌'}\n"
                analysis_result += f"  - EMA50条件: {'✅ 满足' if ema50_condition else '❌ 不满足'}\n"
            else:
                analysis_result += f"  - EMA50数据不足，无法判断\n"
                ema50_condition = False

            # 综合判断首次买入权限
            can_execute_initial_buy = can_initial_buy and cci_cross_condition and ema50_condition
            analysis_result += f"  - 首次买入权限: {'✅ 可执行' if can_execute_initial_buy else '❌ 不可执行'}\n"

            # EXP3突破条件（加仓买入）
            current_exp3 = current_kline_data['exp3'].iloc[-1] if 'exp3' in current_kline_data.columns else np.nan
            prev_exp3 = prev_kline_data['exp3'].iloc[-1] if 'exp3' in prev_kline_data.columns else np.nan
            # current_close已在上面定义，这里不需要重复定义
            prev_close = prev_kline_data['close'].iloc[-1]

            analysis_result += "\n2. 股价上穿EXP3买入条件（加仓买入）:\n"
            if not pd.isna(current_exp3) and not pd.isna(prev_exp3):
                exp3_cross_condition = prev_close <= prev_exp3 and current_close > current_exp3
                analysis_result += f"  - 前一K线收盘({prev_close:.3f}) <= EXP3({prev_exp3:.3f}): {'✅' if prev_close <= prev_exp3 else '❌'}\n"
                analysis_result += f"  - 当前K线收盘({current_close:.3f}) > EXP3({current_exp3:.3f}): {'✅' if current_close > current_exp3 else '❌'}\n"
                analysis_result += f"  - 股价上穿EXP3条件: {'✅ 满足' if exp3_cross_condition else '❌ 不满足'}\n"
                analysis_result += f"  - 加仓买入权限: {'✅ 可执行' if can_add_buy and exp3_cross_condition and not can_initial_buy else '❌ 不可执行'}\n"
            else:
                analysis_result += f"  - EXP3数据不足，无法判断\n"

            # 卖出条件分析（如果有持仓）
            if code in self.position_records:
                position_info = self.position_records[code]
                buy_price = float(position_info['buy_price'])
                quantity = position_info.get('quantity', 10)
                current_close = current_kline_data['close'].iloc[-1]
                profit_amount = (current_close - buy_price) * quantity

                # 安全计算盈利率，避免除零错误
                if buy_price > 0:
                    profit_rate = (current_close - buy_price) / buy_price * 100
                else:
                    profit_rate = 0.0

                analysis_result += f"\n=== 卖出条件分析 ===\n"
                analysis_result += f"📊 持仓信息:\n"
                analysis_result += f"  - 买入价格: {buy_price:.3f}\n"
                analysis_result += f"  - 当前价格: {current_close:.3f}\n"
                analysis_result += f"  - 持仓数量: {quantity}股\n"
                analysis_result += f"  - 总盈亏: {profit_amount:.2f}元\n"
                analysis_result += f"  - 盈利率: {profit_rate:.2f}%\n"

                # 获取卖出状态
                sell_state = position_info.get('sell_state', {
                    'profit_reached': False,
                    'first_exp3_break': False,
                    'first_sell_done': False,
                    'waiting_recovery': False,
                    'recovery_period': 0
                })

                analysis_result += f"\n📋 分批卖出条件检查:\n"

                # 第一阶段：盈利监控
                analysis_result += f"1. 盈利监控阶段:\n"
                analysis_result += f"  - 当前盈利率: {profit_rate:.2f}%\n"
                # 修复显示逻辑：只有当前盈利率>0才显示已盈利，但保留历史盈利状态用于交易逻辑
                current_profit_status = "✅ 已盈利" if profit_rate > 0 else "❌ 未盈利"
                historical_profit_info = f" (历史曾盈利)" if sell_state['profit_reached'] and profit_rate <= 0 else ""
                analysis_result += f"  - 是否开始盈利: {current_profit_status}{historical_profit_info}\n"

                # 修复：只有当前盈利时才进行EXP3跌破监控
                if profit_rate > 0:
                    # 第二阶段：EXP3跌破监控
                    analysis_result += f"\n2. EXP3跌破监控阶段:\n"
                    if not pd.isna(current_exp3) and not pd.isna(prev_exp3):
                        exp3_break = prev_close >= prev_exp3 and current_close < current_exp3
                        analysis_result += f"  - 前一K线收盘({prev_close:.3f}) >= EXP3({prev_exp3:.3f}): {'✅' if prev_close >= prev_exp3 else '❌'}\n"
                        analysis_result += f"  - 当前K线收盘({current_close:.3f}) < EXP3({current_exp3:.3f}): {'✅' if current_close < current_exp3 else '❌'}\n"
                        analysis_result += f"  - EXP3跌破条件: {'⚠️ 触发' if exp3_break else '✅ 正常'}\n"

                        if not sell_state['first_sell_done']:
                            analysis_result += f"  - 卖出状态: 等待第一次EXP3跌破\n"
                            if exp3_break:
                                analysis_result += f"  - 📉 将执行：卖出一半持仓\n"
                        else:
                            # 已经卖出一半的状态
                            analysis_result += f"  - 卖出状态: 已卖出一半，监控收回情况\n"
                            if sell_state['waiting_recovery']:
                                recovery_periods = sell_state.get('recovery_period', 0)
                                analysis_result += f"  - 等待收回周期: {recovery_periods}/2\n"
                                if current_close > current_exp3:
                                    analysis_result += f"  - 📈 股价已收回EXP3之上，等待再次跌破\n"
                                elif recovery_periods >= 2:
                                    analysis_result += f"  - 📉 第二周期未收回，将全部卖出剩余持仓\n"
                                else:
                                    analysis_result += f"  - ⏳ 继续等待收回或达到2周期限制\n"
                            else:
                                analysis_result += f"  - 📈 已收回EXP3之上，等待再次跌破卖出剩余\n"
                                if exp3_break:
                                    analysis_result += f"  - 📉 将执行：卖出剩余全部持仓\n"
                    else:
                        analysis_result += f"  - EXP3数据不足，无法判断跌破条件\n"
                else:
                    analysis_result += f"\n2. EXP3跌破监控: 等待开始盈利后启动\n"

                # 强制卖出条件
                analysis_result += f"\n📋 强制卖出条件检查:\n"
                analysis_result += f"  - 强制止损(-200元): {'⚠️ 触发' if profit_amount <= -200 else '✅ 正常'}\n"

                # 分批止盈状态检查
                profit_state = position_info.get('profit_state', {
                    'first_profit_sell_done': False,
                    'second_profit_sell_done': False
                })

                analysis_result += f"  - 第一次止盈(400元): "
                if profit_state['first_profit_sell_done']:
                    analysis_result += f"✅ 已执行\n"
                elif profit_amount >= 400:
                    analysis_result += f"🎉 触发\n"
                else:
                    analysis_result += f"✅ 正常({profit_amount:.2f}/400元)\n"

                analysis_result += f"  - 第二次止盈(700元): "
                if profit_state['second_profit_sell_done']:
                    analysis_result += f"✅ 已执行\n"
                elif profit_amount >= 700:
                    analysis_result += f"🎉 触发\n"
                else:
                    analysis_result += f"✅ 正常({profit_amount:.2f}/700元)\n"

            else:
                analysis_result += f"\n=== 卖出条件分析 ===\n"
                analysis_result += f"📊 无持仓，无需分析卖出条件\n"

            # 打印到控制台
            print(analysis_result)
            # 写入日志文件
            self.write_log(analysis_result)

        except Exception as e:
            error_msg = f"打印CCI分析结果失败: {str(e)}"
            print(error_msg)
            self.write_log(error_msg)
            return

    def check_cross_signals(self, df, current_price, code):
        try:
            # 获取当前时间
            now = datetime.now()
            current_hour = now.hour
            current_minute = now.minute

            # 检查数据完整性
            if df is None or len(df) < 20:  # CCI需要至少14个数据点
                self.add_record(f"{code} 数据不足，无法检查CCI信号")
                return False, None, None, None

            # 获取当前和前一根K线的数据
            current_kline_data = self.get_current_kline_data(df, current_hour, current_minute)
            prev_kline_data = self.get_prev_kline_data(df, current_hour, current_minute)

            if current_kline_data.empty or prev_kline_data.empty:
                return False, None, None, None

            # 打印CCI分析结果
            self.print_cci_analysis(code, df, current_price)

            # 获取CCI值
            current_cci = current_kline_data['cci'].iloc[-1]
            prev_cci = prev_kline_data['cci'].iloc[-1]

            # 获取EXP3值和价格
            current_exp3 = current_kline_data['exp3'].iloc[-1]
            prev_exp3 = prev_kline_data['exp3'].iloc[-1]
            current_close = current_kline_data['close'].iloc[-1]
            prev_close = prev_kline_data['close'].iloc[-1]

            # 获取EMA50值
            current_ema50 = current_kline_data['ema50'].iloc[-1]

            # 检查CCI上穿-100的买入信号（首次买入）
            # 新增条件：收盘价要小于EMA50的1.01倍
            if prev_cci <= -100 and current_cci > -100:
                # 检查EMA50条件
                if not pd.isna(current_ema50) and current_close < current_ema50 * 1.01:
                    self.add_record(f"🚀 {code} CCI上穿-100买入信号: 前一CCI({prev_cci:.2f}) <= -100, 当前CCI({current_cci:.2f}) > -100, 收盘价({current_close:.3f}) < EMA50*1.01({current_ema50*1.01:.3f})")
                    return True, 'golden', current_cci, 'cci'
                else:
                    if pd.isna(current_ema50):
                        self.add_record(f"❌ {code} CCI上穿-100但EMA50数据不足，跳过买入")
                    else:
                        self.add_record(f"❌ {code} CCI上穿-100但收盘价({current_close:.3f}) >= EMA50*1.01({current_ema50*1.01:.3f})，不满足买入条件")
                    return False, None, None, None

            # 检查股价上穿EXP3的买入信号（加仓买入）
            if not pd.isna(current_exp3) and not pd.isna(prev_exp3):
                # 股价上穿EXP3条件：前一根K线收盘价 <= EXP3，当前K线收盘价 > EXP3
                if prev_close <= prev_exp3 and current_close > current_exp3:
                    self.add_record(f"📈 {code} 股价上穿EXP3买入信号: 前一收盘({prev_close:.3f}) <= EXP3({prev_exp3:.3f}), 当前收盘({current_close:.3f}) > EXP3({current_exp3:.3f})")
                    return True, 'exp3_break', current_close, 'exp3'

            return False, None, None, None

        except Exception as e:
            print(f"检查 {code} 买入信号失败: {str(e)}")
            self.add_record(f"检查 {code} 买入信号失败: {str(e)}")
            return False, None, None, None







    def check_sell_signals(self, df, current_price, code):
        """检查卖出信号 - 新的分批卖出逻辑"""
        try:
            # 获取持仓信息
            if code not in self.position_records:
                return False, None, None, None

            # 检查是否在交易时间
            current_time = datetime.now().time()
            is_trading_time = self.is_trading_time(current_time)

            position_info = self.position_records[code]
            buy_price = position_info['buy_price']
            quantity = position_info.get('quantity', 10)

            # 计算当前盈利率，避免除零错误
            if buy_price > 0:
                profit_rate = (current_price - buy_price) / buy_price * 100
            else:
                profit_rate = 0.0

            # 获取当前和前一根K线的EXP3数据
            current_kline_data = self.get_current_kline_data(df, datetime.now().hour, datetime.now().minute)
            prev_kline_data = self.get_prev_kline_data(df, datetime.now().hour, datetime.now().minute)

            if current_kline_data.empty or prev_kline_data.empty:
                return False, None, None, None

            current_exp3 = current_kline_data['exp3'].iloc[-1] if 'exp3' in current_kline_data.columns else np.nan
            prev_exp3 = prev_kline_data['exp3'].iloc[-1] if 'exp3' in prev_kline_data.columns else np.nan
            current_close = current_kline_data['close'].iloc[-1]
            prev_close = prev_kline_data['close'].iloc[-1]

            # 检查EXP3数据有效性
            if pd.isna(current_exp3) or pd.isna(prev_exp3):
                return False, None, None, None

            # 初始化卖出状态跟踪
            sell_state = position_info.get('sell_state', {
                'profit_reached': False,    # 是否达到盈利
                'first_exp3_break': False,  # 是否第一次跌破EXP3
                'first_sell_done': False,   # 是否已执行第一次卖出
                'waiting_recovery': False,  # 是否等待收回
                'recovery_period': 0        # 等待收回的周期数
            })

            # 检查是否达到盈利（只在交易时间设置状态）
            if profit_rate > 0 and not sell_state['profit_reached']:
                if is_trading_time:
                    sell_state['profit_reached'] = True
                    position_info['sell_state'] = sell_state
                    self.add_record(f"📊 {code} 开始盈利({profit_rate:.2f}%)，开始监控EXP3跌破")
                else:
                    # 非交易时间：只显示信号，不设置状态
                    self.add_record(f"[非交易时间] 📊 {code} 开始盈利({profit_rate:.2f}%) - 仅显示信号")

            # 修复：只有当前盈利时才检查卖出条件
            if profit_rate <= 0:
                return False, None, None, None

            # 检查股价跌破EXP3
            exp3_break = prev_close >= prev_exp3 and current_close < current_exp3

            if not sell_state['first_sell_done']:
                # 第一次卖出逻辑
                if exp3_break:
                    # 注意：调用此方法的股票已经确认是盈利状态，无需再次检查
                    if is_trading_time:
                        # 交易时间：设置状态并返回卖出信号
                        sell_state['first_exp3_break'] = True
                        sell_state['first_sell_done'] = True
                        sell_state['waiting_recovery'] = True
                        sell_state['recovery_period'] = 0
                        position_info['sell_state'] = sell_state

                        self.add_record(f"📉 {code} 第一次跌破EXP3且盈利({profit_rate:.2f}%): 前收盘({prev_close:.3f}) >= EXP3({prev_exp3:.3f}), 当前收盘({current_close:.3f}) < EXP3({current_exp3:.3f})")
                        return True, 'first_exp3_break', current_price, 'half_sell'
                    else:
                        # 非交易时间：只显示信号，不设置状态
                        self.add_record(f"[非交易时间] 📉 {code} 第一次跌破EXP3且盈利({profit_rate:.2f}%) - 仅显示信号")
                        return False, None, None, None

            else:
                # 第二次卖出逻辑（已执行第一次卖出后）
                if sell_state['waiting_recovery']:
                    if is_trading_time:
                        # 交易时间：更新状态
                        sell_state['recovery_period'] += 1
                        position_info['sell_state'] = sell_state

                        # 检查是否收回到EXP3之上
                        if current_close > current_exp3:
                            # 收回了，重置等待状态，等待再次跌破
                            sell_state['waiting_recovery'] = False
                            sell_state['recovery_period'] = 0
                            position_info['sell_state'] = sell_state
                            self.add_record(f"📈 {code} 股价收回EXP3之上({current_close:.3f} > {current_exp3:.3f})，等待再次跌破")

                        elif sell_state['recovery_period'] >= 2:
                            # 第二周期未收回，全部卖出剩余持仓（已确认盈利状态）
                            self.add_record(f"📉 {code} 第二周期未收回EXP3且盈利({profit_rate:.2f}%)，全部卖出剩余持仓")
                            return True, 'second_period_no_recovery', current_price, 'full_sell'
                    else:
                        # 非交易时间：只显示信号，不更新状态
                        if current_close > current_exp3:
                            self.add_record(f"[非交易时间] 📈 {code} 股价收回EXP3之上 - 仅显示信号")
                        elif sell_state['recovery_period'] >= 2:
                            self.add_record(f"[非交易时间] 📉 {code} 第二周期未收回EXP3 - 仅显示信号")

                else:
                    # 等待再次跌破EXP3
                    if exp3_break:
                        # 注意：调用此方法的股票已经确认是盈利状态，无需再次检查
                        if is_trading_time:
                            self.add_record(f"📉 {code} 再次跌破EXP3且盈利({profit_rate:.2f}%)，卖出剩余持仓")
                            return True, 'second_exp3_break', current_price, 'full_sell'
                        else:
                            self.add_record(f"[非交易时间] 📉 {code} 再次跌破EXP3且盈利({profit_rate:.2f}%) - 仅显示信号")
                            return False, None, None, None

            return False, None, None, None

        except Exception as e:
            print(f"检查 {code} 卖出信号失败: {str(e)}")
            self.add_record(f"检查 {code} 卖出信号失败: {str(e)}")
            return False, None, None, None

    def check_tech_sell_signals(self, df, current_price, code):
        """检查技术指标卖出信号 - 不考虑盈亏状态的分批卖出逻辑"""
        try:
            # 获取持仓信息
            if code not in self.position_records:
                return False, None, None, None

            # 检查是否在交易时间
            current_time = datetime.now().time()
            is_trading_time = self.is_trading_time(current_time)

            position_info = self.position_records[code]
            buy_price = position_info['buy_price']
            quantity = position_info.get('quantity', 10)

            # 获取当前和前一根K线的EXP3数据
            current_kline_data = self.get_current_kline_data(df, datetime.now().hour, datetime.now().minute)
            prev_kline_data = self.get_prev_kline_data(df, datetime.now().hour, datetime.now().minute)

            if current_kline_data.empty or prev_kline_data.empty:
                return False, None, None, None

            current_exp3 = current_kline_data['exp3'].iloc[-1] if 'exp3' in current_kline_data.columns else np.nan
            prev_exp3 = prev_kline_data['exp3'].iloc[-1] if 'exp3' in prev_kline_data.columns else np.nan
            current_close = current_kline_data['close'].iloc[-1]
            prev_close = prev_kline_data['close'].iloc[-1]

            # 检查EXP3数据有效性
            if pd.isna(current_exp3) or pd.isna(prev_exp3):
                return False, None, None, None

            # 初始化技术指标卖出状态跟踪（不考虑盈利状态）
            tech_sell_state = position_info.get('tech_sell_state', {
                'first_exp3_break': False,  # 是否第一次跌破EXP3
                'first_sell_done': False,   # 是否已执行第一次卖出
                'waiting_recovery': False,  # 是否等待收回
                'recovery_period': 0        # 等待收回的周期数
            })

            # 检查股价跌破EXP3
            exp3_break = prev_close >= prev_exp3 and current_close < current_exp3

            if not tech_sell_state['first_sell_done']:
                # 第一次卖出逻辑
                if exp3_break:
                    if is_trading_time:
                        # 交易时间：设置状态并返回卖出信号
                        tech_sell_state['first_exp3_break'] = True
                        tech_sell_state['first_sell_done'] = True
                        tech_sell_state['waiting_recovery'] = True
                        tech_sell_state['recovery_period'] = 0
                        position_info['tech_sell_state'] = tech_sell_state

                        self.add_record(f"📉 {code} 技术指标第一次跌破EXP3: 前收盘({prev_close:.3f}) >= EXP3({prev_exp3:.3f}), 当前收盘({current_close:.3f}) < EXP3({current_exp3:.3f})")
                        return True, 'first_exp3_break', current_price, 'half_sell'
                    else:
                        # 非交易时间：只显示信号，不设置状态
                        self.add_record(f"[非交易时间] 📉 {code} 技术指标第一次跌破EXP3 - 仅显示信号")
                        return False, None, None, None

            else:
                # 第二次卖出逻辑（已执行第一次卖出后）
                if tech_sell_state['waiting_recovery']:
                    if is_trading_time:
                        # 交易时间：更新状态
                        tech_sell_state['recovery_period'] += 1
                        position_info['tech_sell_state'] = tech_sell_state

                        # 检查是否收回到EXP3之上
                        if current_close > current_exp3:
                            # 收回了，重置等待状态，等待再次跌破
                            tech_sell_state['waiting_recovery'] = False
                            tech_sell_state['recovery_period'] = 0
                            position_info['tech_sell_state'] = tech_sell_state
                            self.add_record(f"📈 {code} 股价收回EXP3之上({current_close:.3f} > {current_exp3:.3f})，等待再次跌破")

                        elif tech_sell_state['recovery_period'] >= 2:
                            # 第二周期未收回，全部卖出剩余持仓
                            self.add_record(f"📉 {code} 技术指标第二周期未收回EXP3，全部卖出剩余持仓")
                            return True, 'second_period_no_recovery', current_price, 'full_sell'
                    else:
                        # 非交易时间：只显示信号，不更新状态
                        if current_close > current_exp3:
                            self.add_record(f"[非交易时间] 📈 {code} 股价收回EXP3之上 - 仅显示信号")
                        elif tech_sell_state['recovery_period'] >= 2:
                            self.add_record(f"[非交易时间] 📉 {code} 技术指标第二周期未收回EXP3 - 仅显示信号")

                else:
                    # 等待再次跌破EXP3
                    if exp3_break:
                        if is_trading_time:
                            self.add_record(f"📉 {code} 技术指标再次跌破EXP3，卖出剩余持仓")
                            return True, 'second_exp3_break', current_price, 'full_sell'
                        else:
                            self.add_record(f"[非交易时间] 📉 {code} 技术指标再次跌破EXP3 - 仅显示信号")
                            return False, None, None, None

            return False, None, None, None

        except Exception as e:
            print(f"检查 {code} 技术指标卖出信号失败: {str(e)}")
            self.add_record(f"检查 {code} 技术指标卖出信号失败: {str(e)}")
            return False, None, None, None

    def check_temp_sell_signals(self, df, current_price, code):
        """检查暂时卖出信号 - 实际买入后股价跌破EXP3（仅在亏损时执行）"""
        try:
            # 获取持仓信息
            if code not in self.position_records:
                return False, None

            position_info = self.position_records[code]

            # 检查是否为CCI信号记录（仅信号，未实际买入）
            if position_info.get('is_signal_only', False):
                return False, None  # CCI信号记录不进行暂时卖出检查

            # 检查是否已经在技术指标卖出等待重新买回状态
            if code in self.tech_sell_waiting_rebuy:
                return False, None  # 已经暂时卖出，不重复检查

            # 检查当前是否盈利 - 只有亏损时才进行暂时卖出
            buy_price = position_info['buy_price']
            quantity = position_info.get('quantity', 10)
            current_profit = (current_price - buy_price) * quantity

            if current_profit >= 0:
                return False, None  # 盈利时不进行暂时卖出，由技术指标卖出处理

            # 计算指标
            df_with_indicators = self.calculate_indicators(df)

            # 获取最后两根完整K线数据（与买入卖出判断保持一致）
            if len(df_with_indicators) < 2:
                return False, None

            # 去掉最后一根可能未完成的K线，使用完整的K线数据
            completed_df = df_with_indicators.iloc[:-1].copy()

            if len(completed_df) < 2:
                return False, None

            # 使用索引方式获取最后两根完整的K线（与买入卖出判断保持一致）
            current_kline_data = completed_df.iloc[[-1]]  # 最后一根完整K线
            prev_kline_data = completed_df.iloc[[-2]]     # 倒数第二根完整K线

            # 获取EXP3值和收盘价
            current_exp3 = current_kline_data['exp3'].iloc[-1]
            prev_exp3 = prev_kline_data['exp3'].iloc[-1]
            current_close = current_kline_data['close'].iloc[-1]
            prev_close = prev_kline_data['close'].iloc[-1]

            # 检查EXP3数据是否有效
            if pd.isna(current_exp3) or pd.isna(prev_exp3):
                return False, None

            # 检查股价跌破EXP3条件：前一根K线收盘价 >= EXP3，当前K线收盘价 < EXP3
            exp3_break = prev_close >= prev_exp3 and current_close < current_exp3

            if exp3_break:
                self.add_record(f"⚠️ {code} 亏损状态下跌破EXP3，执行暂时卖出: 前收盘({prev_close:.3f}) >= EXP3({prev_exp3:.3f}), 当前收盘({current_close:.3f}) < EXP3({current_exp3:.3f}), 当前亏损{current_profit:.2f}元")
                return True, 'temp_sell'

            return False, None

        except Exception as e:
            print(f"检查 {code} 暂时卖出信号失败: {str(e)}")
            self.add_record(f"检查 {code} 暂时卖出信号失败: {str(e)}")
            return False, None



    def get_current_kline_data(self, df, current_hour=None, current_minute=None):
        """获取当前K线数据（使用索引方式）"""
        try:
            # 检查数据是否足够
            if len(df) < 1:
                print(f"获取当前K线数据失败: 数据不足，只有{len(df)}根K线")
                return pd.DataFrame()

            # 判断是否在交易时段（15:00收盘，15:00以后为收盘后）
            current_time = datetime.now()
            is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') < '11:30:00') or \
                            ('13:00:00' <= current_time.strftime('%H:%M:%S') < '15:00:00')

            if is_trading_time:
                # 交易时段：去掉最后一根可能未完成的K线
                if len(df) < 2:
                    print(f"获取当前K线数据失败: 交易时段数据不足，只有{len(df)}根K线")
                    return pd.DataFrame()
                completed_df = df.iloc[:-1].copy()
                if len(completed_df) < 1:
                    print(f"获取当前K线数据失败: 完整K线数据不足")
                    return pd.DataFrame()
                # 使用索引方式获取最后一根完整的K线
                return completed_df.iloc[[-1]]
            else:
                # 非交易时段：使用所有K线数据，包括最后一根
                return df.iloc[[-1]]

        except Exception as e:
            print(f"获取当前K线数据失败: {str(e)}")
            return pd.DataFrame()

    def get_prev_kline_data(self, df, current_hour=None, current_minute=None):
        """获取前一根K线数据（使用索引方式）"""
        try:
            # 判断是否在交易时段（15:00收盘，15:00以后为收盘后）
            current_time = datetime.now()
            is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') < '11:30:00') or \
                            ('13:00:00' <= current_time.strftime('%H:%M:%S') < '15:00:00')

            if is_trading_time:
                # 交易时段：去掉最后一根可能未完成的K线
                if len(df) < 3:
                    print(f"获取前一根K线数据失败: 交易时段数据不足，只有{len(df)}根K线")
                    return pd.DataFrame()
                completed_df = df.iloc[:-1].copy()
                if len(completed_df) < 2:
                    print(f"获取前一根K线数据失败: 完整K线数据不足")
                    return pd.DataFrame()
                # 使用索引方式获取倒数第二根完整的K线
                return completed_df.iloc[[-2]]
            else:
                # 非交易时段：使用所有K线数据
                if len(df) < 2:
                    print(f"获取前一根K线数据失败: 非交易时段数据不足，只有{len(df)}根K线")
                    return pd.DataFrame()
                # 使用索引方式获取倒数第二根K线
                return df.iloc[[-2]]

        except Exception as e:
            print(f"获取前一根K线数据失败: {str(e)}")
            return pd.DataFrame()

    def start_price_refresh(self):
        """启动持仓价格定时刷新"""
        try:
            # 检查日期是否变化，如果是新的一天则重置标记
            current_date = datetime.now().strftime('%Y%m%d')
            if current_date != self.current_date:
                self.current_date = current_date
                self.closing_price_updated = False
                self.refresh_stopped_logged = False
                self.daily_stats_saved = False
                self.add_record(f"📅 日期变更为 {current_date}，重置收盘价格更新标记")

            # 获取当前时间
            current_time = datetime.now().time()
            is_trading_time = self.is_trading_time(current_time)

            # 更新持仓列表（包含价格获取）
            self.update_position_list()

            # 检查是否需要保存每日统计数据（15:10）
            if (current_time >= datetime.strptime('15:10:00', '%H:%M:%S').time() and
                current_time <= datetime.strptime('15:11:00', '%H:%M:%S').time() and
                not self.daily_stats_saved):
                self.save_daily_stats()

            # 检查持仓盈利情况和等待盈利卖出
            if self.position_records:
                for code, info in list(self.position_records.items()):
                    if is_trading_time:
                        # 交易时间内，每次都获取最新价格
                        current_price = self.get_current_price(code)
                    else:
                        # 收盘后，尝试从持仓记录中获取刚刚更新的价格，避免重复下载
                        current_price = info.get('current_price')
                        if not current_price:
                            # 如果没有缓存价格，才重新获取
                            current_price = self.get_current_price(code)

                    if current_price:
                        buy_price = info['buy_price']

                        # 检查是否在等待盈利卖出状态
                        if code in self.waiting_for_profit_sell:
                            # 如果已经盈利，执行卖出
                            if current_price > buy_price:
                                self.add_record(f"{code} 等待盈利卖出条件满足，当前价格{current_price:.3f} > 买入价格{buy_price:.3f}")
                                self.execute_sell(code, current_price, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                                # 移除等待盈利卖出标记
                                del self.waiting_for_profit_sell[code]
                                continue

            # 保存交易数据（如果有变化）
            self.save_trading_data()

            # 更新交易汇总（复用已获取的价格）
            self.update_trade_summary()

        except Exception as e:
            self.add_record(f"刷新持仓价格时出错: {str(e)}")

        # 无论是否出错，都要继续定时刷新
        try:
            current_time = datetime.now().time()
            is_trading_time = self.is_trading_time(current_time)

            if is_trading_time:
                # 交易时间内：每10秒刷新一次
                self.root.after(10000, self.start_price_refresh)
            else:
                # 非交易时间的处理逻辑
                if (current_time >= datetime.strptime('15:01:00', '%H:%M:%S').time() and
                    current_time <= datetime.strptime('15:02:00', '%H:%M:%S').time()):
                    # 15:01-15:02期间，进行收盘价格更新
                    if not getattr(self, 'closing_price_updated', False):
                        self.add_record("📊 收盘后进行一次性价格更新...")
                        self.closing_price_updated = True
                    # 5秒后再次检查
                    self.root.after(5000, self.start_price_refresh)
                elif current_time < datetime.strptime('15:02:00', '%H:%M:%S').time():
                    # 还没到15:01，继续等待
                    self.root.after(30000, self.start_price_refresh)  # 30秒后再检查
                else:
                    # 15:02之后，停止定时刷新
                    if not getattr(self, 'refresh_stopped_logged', False):
                        self.add_record("✅ 收盘后价格更新完成，停止定时刷新")
                        self.refresh_stopped_logged = True
        except Exception as e:
            self.add_record(f"定时刷新调度出错: {str(e)}")
            # 即使调度出错，也要尝试继续
            self.root.after(10000, self.start_price_refresh)

    def save_daily_stats(self):
        """保存每日统计数据"""
        try:
            # 获取当前日期
            current_date = datetime.now().strftime('%Y%m%d')
            
            # 如果日期变化，重置统计数据和保存状态
            if current_date != self.current_date:
                self.max_positions_today = 0
                self.max_profit_today = 0.0
                self.max_loss_today = 0.0
                self.daily_max_float_profit = 0.0
                self.daily_max_float_loss = 0.0
                self.daily_total_profit = 0.0
                self.single_stock_max_profit = 0.0  # 重置单只股票最大浮盈
                self.single_stock_max_loss = 0.0    # 重置单只股票最大浮亏
                self.daily_stats_saved = False
                self.current_date = current_date
                return
            
            # 如果已经保存过今天的统计，则不重复保存
            if self.daily_stats_saved:
                return
            
            # 准备统计数据
            daily_stats = {
                'date': current_date,
                'max_positions': self.max_positions_today,
                'max_profit': round(self.max_profit_today, 2),
                'max_loss': round(self.max_loss_today, 2),
                'single_stock_max_profit': round(self.single_stock_max_profit, 2),
                'single_stock_max_loss': round(self.single_stock_max_loss, 2),
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
            # 加载现有的交易历史数据
            trading_data = {}
            if os.path.exists('trading_history.json'):
                try:
                    with open('trading_history.json', 'r', encoding='utf-8') as f:
                        trading_data = json.load(f)
                except:
                    pass
            
            # 确保daily_stats键存在
            if 'daily_stats' not in trading_data:
                trading_data['daily_stats'] = []
            
            # 添加今日统计数据
            trading_data['daily_stats'].append(daily_stats)
            
            # 保存更新后的数据
            with open('trading_history.json', 'w', encoding='utf-8') as f:
                json.dump(trading_data, f, ensure_ascii=False, indent=2)
            
            self.add_record(f"已保存今日统计数据 - 最大持仓:{self.max_positions_today} "
                          f"最大盈利:{self.max_profit_today:.2f} "
                          f"最大亏损:{self.max_loss_today:.2f} "
                          f"单股最大浮盈:{self.single_stock_max_profit:.2f} "
                          f"单股最大浮亏:{self.single_stock_max_loss:.2f}")
            
            # 标记为已保存
            self.daily_stats_saved = True
            
        except Exception as e:
            self.add_record(f"保存每日统计数据失败: {str(e)}")
            print(f"保存每日统计数据失败: {str(e)}")  # 添加控制台输出以便调试

    def load_state(self):
        """加载状态信息"""
        try:
            state_file = f"交易状态_{self.today}.json"
            if os.path.exists(state_file):
                with open(state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    
                    # 加载等待突破记录
                    self.waiting_for_exp3_break = state.get('waiting_for_exp3_break', {})
                    # 加载等待盈利卖出记录
                    self.waiting_for_profit_sell = state.get('waiting_for_profit_sell', {})
                    # 加载止盈监控记录
                    self.profit_taking_monitor = state.get('profit_taking_monitor', {})
                    # 加载技术指标卖出等待重新买回记录
                    self.tech_sell_waiting_rebuy = state.get('tech_sell_waiting_rebuy', {})
                    
                    # 加载统计数据
                    self.max_positions_today = state.get('max_positions_today', 0)
                    self.max_profit_today = state.get('max_profit_today', 0.0)
                    self.max_loss_today = state.get('max_loss_today', 0.0)
                    self.daily_max_float_profit = state.get('daily_max_float_profit', 0.0)
                    self.daily_max_float_loss = state.get('daily_max_float_loss', 0.0)
                    self.daily_total_profit = state.get('daily_total_profit', 0.0)
                    self.single_stock_max_profit = state.get('single_stock_max_profit', 0.0)
                    self.single_stock_max_loss = state.get('single_stock_max_loss', 0.0)
                    
                    print(f"已加载状态信息 - 等待突破: {len(self.waiting_for_exp3_break)} 条")
                    print(f"最大持仓: {self.max_positions_today}, 当日最大浮盈: {self.daily_max_float_profit:.2f}")
            else:
                print("未找到状态文件，使用初始值")
        except Exception as e:
            print(f"加载状态信息失败: {str(e)}")
    
    def save_state(self):
        """保存状态信息"""
        try:
            state = {
                # 等待和监控状态
                'waiting_for_exp3_break': self.waiting_for_exp3_break,
                'waiting_for_profit_sell': self.waiting_for_profit_sell,
                'profit_taking_monitor': self.profit_taking_monitor,
                'tech_sell_waiting_rebuy': self.tech_sell_waiting_rebuy,

                # 统计数据
                'max_positions_today': self.max_positions_today,
                'max_profit_today': self.max_profit_today,
                'max_loss_today': self.max_loss_today,
                'daily_max_float_profit': self.daily_max_float_profit,
                'daily_max_float_loss': self.daily_max_float_loss,
                'daily_total_profit': self.daily_total_profit,
                'single_stock_max_profit': self.single_stock_max_profit,
                'single_stock_max_loss': self.single_stock_max_loss
            }
            
            with open(f"交易状态_{self.today}.json", 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存状态信息失败: {str(e)}")
            self.add_record(f"保存状态信息失败: {str(e)}")

    def show_trading_statistics(self):
        """显示当日交易统计报表"""
        try:
            # 创建统计窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("当日交易统计报表")
            stats_window.geometry("800x600")

            # 创建主框架
            main_frame = ttk.Frame(stats_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 创建标题
            title_label = ttk.Label(main_frame, text=f"交易统计报表 - {datetime.now().strftime('%Y年%m月%d日')}",
                                  font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))

            # 创建滚动框架
            canvas = tk.Canvas(main_frame)
            scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 计算统计数据
            stats_data = self.calculate_trading_statistics()

            # 显示总体统计
            self.create_overall_stats_section(scrollable_frame, stats_data)

            # 显示分股票统计
            self.create_stock_stats_section(scrollable_frame, stats_data)

            # 布局滚动组件
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 绑定鼠标滚轮事件
            def _on_mousewheel(event):
                try:
                    # 检查canvas是否仍然有效
                    if canvas.winfo_exists():
                        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                except (tk.TclError, AttributeError):
                    # Canvas已被销毁，忽略事件
                    pass

            # 只在canvas上绑定滚轮事件，而不是全局绑定
            canvas.bind("<MouseWheel>", _on_mousewheel)

            # 确保canvas能够获得焦点以接收滚轮事件
            canvas.focus_set()

            # 当鼠标进入canvas时设置焦点
            def _on_enter(event):
                canvas.focus_set()
            canvas.bind("<Enter>", _on_enter)

            # 在窗口关闭时清理事件绑定
            def _on_window_close():
                try:
                    canvas.unbind("<MouseWheel>")
                    canvas.unbind("<Enter>")
                except (tk.TclError, AttributeError):
                    pass
                stats_window.destroy()

            stats_window.protocol("WM_DELETE_WINDOW", _on_window_close)

            # 添加导出按钮
            export_frame = ttk.Frame(main_frame)
            export_frame.pack(fill="x", pady=(10, 0))

            export_button = ttk.Button(export_frame, text="导出统计报表",
                                     command=lambda: self.export_statistics_report(stats_data))
            export_button.pack(side="right")

        except Exception as e:
            messagebox.showerror("错误", f"显示统计报表失败: {str(e)}")
            print(f"显示统计报表失败: {str(e)}")

    def calculate_trading_statistics(self):
        """计算交易统计数据"""
        try:
            # 按股票分组统计
            stock_stats = {}

            for record in self.trade_records:
                code = record['code']
                profit = record['profit']

                if code not in stock_stats:
                    stock_stats[code] = {
                        'total_trades': 0,
                        'profit_trades': 0,
                        'loss_trades': 0,
                        'total_profit': 0.0,
                        'total_loss': 0.0,
                        'max_profit': 0.0,
                        'max_loss': 0.0,
                        'avg_profit': 0.0,
                        'win_rate': 0.0,
                        'trades': []
                    }

                stats = stock_stats[code]
                stats['total_trades'] += 1
                stats['trades'].append(record)

                if profit > 0:
                    stats['profit_trades'] += 1
                    stats['total_profit'] += profit
                    stats['max_profit'] = max(stats['max_profit'], profit)
                else:
                    stats['loss_trades'] += 1
                    stats['total_loss'] += abs(profit)
                    stats['max_loss'] = max(stats['max_loss'], abs(profit))

            # 计算衍生指标
            for code, stats in stock_stats.items():
                if stats['total_trades'] > 0:
                    stats['win_rate'] = (stats['profit_trades'] / stats['total_trades']) * 100
                    net_profit = stats['total_profit'] - stats['total_loss']
                    stats['avg_profit'] = net_profit / stats['total_trades']

            # 计算总体统计
            total_trades = len(self.trade_records)
            total_profit_trades = sum(1 for r in self.trade_records if r['profit'] > 0)
            total_loss_trades = sum(1 for r in self.trade_records if r['profit'] <= 0)
            total_profit = sum(r['profit'] for r in self.trade_records if r['profit'] > 0)
            total_loss = sum(abs(r['profit']) for r in self.trade_records if r['profit'] <= 0)
            net_profit = sum(r['profit'] for r in self.trade_records)

            overall_stats = {
                'total_trades': total_trades,
                'profit_trades': total_profit_trades,
                'loss_trades': total_loss_trades,
                'total_profit': total_profit,
                'total_loss': total_loss,
                'net_profit': net_profit,
                'win_rate': (total_profit_trades / total_trades * 100) if total_trades > 0 else 0,
                'avg_profit_per_trade': net_profit / total_trades if total_trades > 0 else 0,
                'max_profit': max([r['profit'] for r in self.trade_records], default=0),
                'max_loss': max([abs(r['profit']) for r in self.trade_records if r['profit'] < 0], default=0),
                'current_positions': len(self.position_records),
                'max_positions_today': self.max_positions_today,
                'daily_max_float_profit': self.daily_max_float_profit,
                'daily_max_float_loss': self.daily_max_float_loss
            }

            return {
                'overall': overall_stats,
                'by_stock': stock_stats
            }

        except Exception as e:
            print(f"计算统计数据失败: {str(e)}")
            return {'overall': {}, 'by_stock': {}}

    def create_overall_stats_section(self, parent, stats_data):
        """创建总体统计部分"""
        overall = stats_data['overall']

        # 总体统计框架
        overall_frame = ttk.LabelFrame(parent, text="总体统计", padding=10)
        overall_frame.pack(fill="x", pady=(0, 10))

        # 创建两列布局
        left_frame = ttk.Frame(overall_frame)
        left_frame.pack(side="left", fill="both", expand=True)

        right_frame = ttk.Frame(overall_frame)
        right_frame.pack(side="right", fill="both", expand=True)

        # 左列统计
        left_stats = [
            ("总交易次数", f"{overall.get('total_trades', 0)}次"),
            ("盈利次数", f"{overall.get('profit_trades', 0)}次"),
            ("亏损次数", f"{overall.get('loss_trades', 0)}次"),
            ("胜率", f"{overall.get('win_rate', 0):.1f}%"),
            ("平均每笔盈亏", f"{overall.get('avg_profit_per_trade', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(left_stats):
            ttk.Label(left_frame, text=f"{label}:").grid(row=i, column=0, sticky="w", padx=(0, 10))
            ttk.Label(left_frame, text=value, font=("Arial", 9, "bold")).grid(row=i, column=1, sticky="w")

        # 右列统计
        right_stats = [
            ("总盈利", f"{overall.get('total_profit', 0):.2f}元"),
            ("总亏损", f"{overall.get('total_loss', 0):.2f}元"),
            ("净盈亏", f"{overall.get('net_profit', 0):.2f}元"),
            ("单笔最大盈利", f"{overall.get('max_profit', 0):.2f}元"),
            ("单笔最大亏损", f"{overall.get('max_loss', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(right_stats):
            ttk.Label(right_frame, text=f"{label}:").grid(row=i, column=0, sticky="w", padx=(0, 10))
            color = "green" if "盈利" in label or ("净盈亏" in label and overall.get('net_profit', 0) > 0) else "red" if "亏损" in label or ("净盈亏" in label and overall.get('net_profit', 0) < 0) else "black"
            ttk.Label(right_frame, text=value, font=("Arial", 9, "bold"), foreground=color).grid(row=i, column=1, sticky="w")

        # 持仓统计
        position_frame = ttk.LabelFrame(parent, text="持仓统计", padding=10)
        position_frame.pack(fill="x", pady=(0, 10))

        position_stats = [
            ("当前持仓数", f"{overall.get('current_positions', 0)}只"),
            ("今日最大持仓数", f"{overall.get('max_positions_today', 0)}只"),
            ("今日最大浮盈", f"{overall.get('daily_max_float_profit', 0):.2f}元"),
            ("今日最大浮亏", f"{overall.get('daily_max_float_loss', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(position_stats):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(position_frame, text=f"{label}:").grid(row=row, column=col, sticky="w", padx=(0, 10), pady=2)
            ttk.Label(position_frame, text=value, font=("Arial", 9, "bold")).grid(row=row, column=col+1, sticky="w", padx=(0, 20), pady=2)

    def create_stock_stats_section(self, parent, stats_data):
        """创建分股票统计部分"""
        by_stock = stats_data['by_stock']

        if not by_stock:
            no_data_frame = ttk.LabelFrame(parent, text="分股票统计", padding=10)
            no_data_frame.pack(fill="x", pady=(0, 10))
            ttk.Label(no_data_frame, text="暂无交易记录").pack()
            return

        # 分股票统计框架
        stock_frame = ttk.LabelFrame(parent, text="分股票统计", padding=10)
        stock_frame.pack(fill="both", expand=True, pady=(0, 10))

        # 创建表格
        columns = ("股票代码", "交易次数", "盈利次数", "亏损次数", "胜率", "净盈亏", "平均盈亏", "最大盈利", "最大亏损")

        tree = ttk.Treeview(stock_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        column_widths = [80, 80, 80, 80, 80, 100, 100, 100, 100]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            tree.heading(col, text=col)
            tree.column(col, width=width, anchor="center")

        # 添加滚动条
        stock_scrollbar = ttk.Scrollbar(stock_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=stock_scrollbar.set)

        # 按净盈亏排序
        sorted_stocks = sorted(by_stock.items(),
                             key=lambda x: x[1]['total_profit'] - x[1]['total_loss'],
                             reverse=True)

        # 填充数据
        for code, stats in sorted_stocks:
            net_profit = stats['total_profit'] - stats['total_loss']
            values = (
                code,
                f"{stats['total_trades']}次",
                f"{stats['profit_trades']}次",
                f"{stats['loss_trades']}次",
                f"{stats['win_rate']:.1f}%",
                f"{net_profit:.2f}元",
                f"{stats['avg_profit']:.2f}元",
                f"{stats['max_profit']:.2f}元",
                f"{stats['max_loss']:.2f}元"
            )

            # 根据净盈亏设置行颜色
            item = tree.insert("", "end", values=values)
            if net_profit > 0:
                tree.set(item, "净盈亏", f"+{net_profit:.2f}元")
            elif net_profit < 0:
                tree.set(item, "净盈亏", f"{net_profit:.2f}元")

        # 布局
        tree.pack(side="left", fill="both", expand=True)
        stock_scrollbar.pack(side="right", fill="y")

        # 添加双击事件查看详细交易记录
        def on_double_click(event):
            item = tree.selection()[0]
            code = tree.item(item, "values")[0]
            self.show_stock_detail(code, by_stock[code])

        tree.bind("<Double-1>", on_double_click)

    def show_stock_detail(self, code, stock_stats):
        """显示单个股票的详细交易记录"""
        try:
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"{code} 详细交易记录")
            detail_window.geometry("900x500")

            # 创建主框架
            main_frame = ttk.Frame(detail_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 股票统计摘要
            summary_frame = ttk.LabelFrame(main_frame, text=f"{code} 交易摘要", padding=10)
            summary_frame.pack(fill="x", pady=(0, 10))

            net_profit = stock_stats['total_profit'] - stock_stats['total_loss']
            summary_text = (
                f"总交易: {stock_stats['total_trades']}次  "
                f"盈利: {stock_stats['profit_trades']}次  "
                f"亏损: {stock_stats['loss_trades']}次  "
                f"胜率: {stock_stats['win_rate']:.1f}%  "
                f"净盈亏: {net_profit:.2f}元"
            )
            ttk.Label(summary_frame, text=summary_text, font=("Arial", 10, "bold")).pack()

            # 详细交易记录表格
            detail_frame = ttk.LabelFrame(main_frame, text="详细交易记录", padding=10)
            detail_frame.pack(fill="both", expand=True)

            columns = ("买入时间", "卖出时间", "买入价", "卖出价", "数量", "盈亏", "盈亏率", "手续费")
            detail_tree = ttk.Treeview(detail_frame, columns=columns, show="headings")

            # 设置列标题和宽度
            detail_widths = [80, 80, 80, 80, 60, 80, 80, 80]
            for col, width in zip(columns, detail_widths):
                detail_tree.heading(col, text=col)
                detail_tree.column(col, width=width, anchor="center")

            # 添加滚动条
            detail_scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=detail_tree.yview)
            detail_tree.configure(yscrollcommand=detail_scrollbar.set)

            # 填充交易记录
            for trade in stock_stats['trades']:
                buy_price = trade.get('buy_price', 0)
                sell_price = trade.get('sell_price', 0)
                quantity = trade.get('quantity', 0)
                profit = trade.get('profit', 0)
                commission = trade.get('commission', 0)

                # 计算盈亏率
                profit_rate = (profit / (buy_price * quantity) * 100) if buy_price > 0 and quantity > 0 else 0

                values = (
                    trade.get('buy_time', ''),
                    trade.get('sell_time', ''),
                    f"{buy_price:.3f}",
                    f"{sell_price:.3f}",
                    str(quantity),
                    f"{profit:.2f}",
                    f"{profit_rate:.2f}%",
                    f"{commission:.2f}"
                )

                detail_tree.insert("", "end", values=values)

            # 布局
            detail_tree.pack(side="left", fill="both", expand=True)
            detail_scrollbar.pack(side="right", fill="y")

        except Exception as e:
            messagebox.showerror("错误", f"显示股票详细信息失败: {str(e)}")

    def export_statistics_report(self, stats_data):
        """导出统计报表到CSV文件"""
        try:
            import csv

            # 获取当前日期作为文件名
            date_str = datetime.now().strftime('%Y%m%d')
            filename = f"交易统计报表_{date_str}.csv"

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入总体统计
                writer.writerow(['=== 总体统计 ==='])
                overall = stats_data['overall']
                writer.writerow(['总交易次数', f"{overall.get('total_trades', 0)}次"])
                writer.writerow(['盈利次数', f"{overall.get('profit_trades', 0)}次"])
                writer.writerow(['亏损次数', f"{overall.get('loss_trades', 0)}次"])
                writer.writerow(['胜率', f"{overall.get('win_rate', 0):.1f}%"])
                writer.writerow(['总盈利', f"{overall.get('total_profit', 0):.2f}元"])
                writer.writerow(['总亏损', f"{overall.get('total_loss', 0):.2f}元"])
                writer.writerow(['净盈亏', f"{overall.get('net_profit', 0):.2f}元"])
                writer.writerow(['平均每笔盈亏', f"{overall.get('avg_profit_per_trade', 0):.2f}元"])
                writer.writerow(['单笔最大盈利', f"{overall.get('max_profit', 0):.2f}元"])
                writer.writerow(['单笔最大亏损', f"{overall.get('max_loss', 0):.2f}元"])
                writer.writerow([])

                # 写入分股票统计
                writer.writerow(['=== 分股票统计 ==='])
                writer.writerow(['股票代码', '交易次数', '盈利次数', '亏损次数', '胜率(%)', '净盈亏(元)', '平均盈亏(元)', '最大盈利(元)', '最大亏损(元)'])

                by_stock = stats_data['by_stock']
                sorted_stocks = sorted(by_stock.items(),
                                     key=lambda x: x[1]['total_profit'] - x[1]['total_loss'],
                                     reverse=True)

                for code, stats in sorted_stocks:
                    net_profit = stats['total_profit'] - stats['total_loss']
                    writer.writerow([
                        code,
                        stats['total_trades'],
                        stats['profit_trades'],
                        stats['loss_trades'],
                        f"{stats['win_rate']:.1f}",
                        f"{net_profit:.2f}",
                        f"{stats['avg_profit']:.2f}",
                        f"{stats['max_profit']:.2f}",
                        f"{stats['max_loss']:.2f}"
                    ])

            messagebox.showinfo("成功", f"统计报表已导出至: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出统计报表失败: {str(e)}")




def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    # 计算MTR
    high_low = df['high'] - df['low']
    high_close = abs(df['close'].shift() - df['high'])
    low_close = abs(df['close'].shift() - df['low'])
    mtr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    
    # 计算ATR - 使用简单移动平均
    atr = mtr.rolling(window=period).mean()
    
    return atr

if __name__ == "__main__":
    try:
        app = TimeSeriesViewer()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}") 